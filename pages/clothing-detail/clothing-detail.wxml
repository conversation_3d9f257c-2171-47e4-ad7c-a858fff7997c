<!-- 服装详情页面 -->
<view class="clothing-detail-container">
  <!-- 服装信息卡片 -->
  <view class="clothing-info-card">
    <clothing-info-card
      wx:if="{{!isOem}}"
      clothingInfo="{{clothingInfo}}"
      isOem="{{false}}"
      showDetailInfo="{{true}}"
      disableNavigation="{{true}}">
    </clothing-info-card>

    <clothing-info-card
      wx:if="{{isOem}}"
      oemClothingInfo="{{clothingInfo}}"
      isOem="{{true}}"
      showDetailInfo="{{true}}"
      disableNavigation="{{true}}">
    </clothing-info-card>
  </view>

  <!-- 选项卡卡片 -->
  <view class="tabs-card">
    <!-- 选项卡头部 -->
    <view class="tabs-header">
      <view
        class="tab-item {{activeTab === 'inventory' ? 'active' : ''}}"
        bind:tap="switchTab"
        data-tab="inventory">
        <view class="tab-text">库存明细</view>
      </view>
      <view
        class="tab-item {{activeTab === 'logs' ? 'active' : ''}}"
        bind:tap="switchTab"
        data-tab="logs">
        <view class="tab-text">仓库日志</view>
      </view>
    </view>

    <!-- 选项卡内容 -->
    <view class="tabs-content">
      <!-- 库存明细选项卡 -->
      <view wx:if="{{activeTab === 'inventory'}}" class="tab-content">
        <view class="inventory-header">
          <view class="inventory-stats">
            <view class="stats-text">总计：{{totalPackages || 0}}包 / {{totalPieces || 0}}件</view>
          </view>
        </view>

        <view wx:if="{{loadingInventory}}" class="loading-state">
          <van-loading type="spinner" />
          <view class="loading-text">加载中...</view>
        </view>

        <view wx:elif="{{inventoryList.length === 0}}" class="empty-state">
          <van-empty description="暂无库存数据" />
        </view>

        <scroll-view wx:else class="inventory-list" scroll-y>
          <view wx:for="{{inventoryList}}" wx:key="warehouse_id" class="inventory-item">
            <view class="warehouse-info">
              <view class="warehouse-name">{{item.warehouse_name}}</view>
              <view class="warehouse-address">{{item.warehouse_address}}</view>
            </view>
            <view class="stock-details">
              <view class="stock-info">{{item.total_pieces || 0}}件</view>
              <view class="detail-value">{{item.total_packages || 0}}包</view>
            </view>
          </view>
        </scroll-view>
      </view>

      <!-- 仓库日志选项卡 -->
      <view wx:if="{{activeTab === 'logs'}}" class="tab-content">
        <!-- 日历表组件 -->
        <view class="calendar-section">
          <calendar-table
            id="calendar-table"
            clothing-id="{{isOem ? oemClothingId : clothingId}}"
            show-query-button="{{true}}"
            bind:query="onCalendarQuery">
          </calendar-table>
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 全局加载状态 -->
<van-loading wx:if="{{loading}}" type="spinner" />
