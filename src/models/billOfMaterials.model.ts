import { Schema, model, Types } from 'mongoose'

/**
 * 物料清单模型
 */
export const BillOfMaterialsSchema = new Schema(
  {
    bom_id: {
      type: String,
      required: true,
      unique: true,
    },
    clothing_id: {
      type: String,
      required: true,
    },
    bom_year: {
      type: String,
      required: true,
    },
    version: {
      type: String,
      required: false,
      default: '1.0', // 版本号
    },
    // 物料清单项
    bom_items: [
      {
        material_id: {
          type: String,
          required: true,
        },
        quantity: {
          type: Number,
          required: true, // 用量
        },
        unit: {
          type: String,
          required: true, // 单位
        },
        unit_price: {
          type: Number,
          required: false, // 单价（可从物料信息获取）
        },
        total_cost: {
          type: Number,
          required: false, // 小计成本
        },
        remark: {
          type: String,
          required: false,
        },
        material_name: {
          type: String,
          required: false, // 物料名称（前端显示用）
        },
        material_specification: {
          type: String,
          required: false, // 规格型号（前端显示用）
        }
      }
    ],
    total_material_cost: {
      type: Number,
      required: false,
      default: 0, // 总物料成本
    },
    status: {
      type: String,
      required: false,
      default: 'draft', // draft:草稿, active:生效, archived:归档
    },
    remark: {
      type: String,
      required: false,
    },
    createTime: {
      type: Date,
      default: Date.now,
    },
    lastChangeTime: {
      type: Date,
      required: false,
    },
  },
  {
    collection: 'bill_of_materials',
    versionKey: false,
    timestamps: true,
  }
)

// 物料清单项接口
export interface BomItem {
  material_id: string
  quantity: number
  unit: string
  unit_price?: number
  total_cost?: number
  remark?: string
  material_name?: string // 物料名称（前端显示用）
  material_specification?: string // 规格型号（前端显示用）
}

export interface BillOfMaterials {
  _id?: Types.ObjectId
  id?: string
  bom_id: string
  clothing_id: string
  bom_year: string
  version?: string
  bom_items?: BomItem[]
  total_material_cost?: number
  status?: string
  remark?: string
  createTime?: Date
  lastChangeTime?: Date
}

// 模型名称为 'BillOfMaterials'
export const BillOfMaterialsModel = model<BillOfMaterials>('BillOfMaterials', BillOfMaterialsSchema)
