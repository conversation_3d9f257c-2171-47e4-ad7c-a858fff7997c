// 物料清单相关类型定义

export interface ApiResponse<T = any> {
  code: number
  data: T
  message: string
}

// 物料分类
export interface MaterialCategory {
  _id?: string
  id?: string
  category_id: string
  category_name: string
  parent_category_id?: string
  level: number
  sort_order?: number
  description?: string
  state?: string
  children?: MaterialCategory[] // 前端使用的子分类
  createTime?: string | Date
  lastChangeTime?: string | Date
}

// 物料年度价格
export interface MaterialYearlyPrice {
  year: string
  price: number
  effective_date?: string | Date
  supplier?: string
}

// 物料信息
export interface Material {
  _id?: string
  id?: string
  material_id: string
  material_name: string
  category_id: string
  specification?: string
  unit: string
  current_price?: number
  yearly_prices?: MaterialYearlyPrice[]
  description?: string
  state?: string
  category_name?: string // 前端显示用
  createTime?: string | Date
  lastChangeTime?: string | Date
}

// 物料清单项
export interface BomItem {
  material_id: string
  quantity: number
  unit: string
  unit_price?: number
  total_cost?: number
  remark?: string
  material_name?: string // 前端显示用
  material_specification?: string // 前端显示用
}

// 物料清单
export interface BillOfMaterials {
  _id?: string
  id?: string
  bom_id: string
  clothing_id: string
  bom_year: string
  version?: string
  bom_items?: BomItem[]
  total_material_cost?: number
  status?: string
  remark?: string
  clothing_name?: string // 前端显示用
  createTime?: string | Date
  lastChangeTime?: string | Date
}

// 查询参数
export interface QueryMaterialParams {
  material_id?: string
  material_name?: string
  category_id?: string
  years?: string[]
  page?: number
  limit?: number
}

export interface QueryBomParams {
  clothing_id?: string
  bom_year?: string
  status?: string
  page?: number
  limit?: number
}

export interface QueryMaterialCategoryParams {
  category_name?: string
  level?: number
  parent_category_id?: string
  page?: number
  limit?: number
}

// 列表响应
export interface MaterialListResponse {
  total: number
  page: number
  limit: number
  materialList: Material[]
}

export interface BomListResponse {
  total: number
  page: number
  limit: number
  bomList: BillOfMaterials[]
}

export interface MaterialCategoryListResponse {
  total: number
  page: number
  limit: number
  categoryList: MaterialCategory[]
}

// 创建和更新参数
export interface CreateMaterialParams {
  material_id: string
  material_name: string
  category_id: string
  specification?: string
  unit: string
  current_price?: number
  yearly_prices?: MaterialYearlyPrice[]
  description?: string
  state?: string
}

export interface UpdateMaterialParams extends Partial<CreateMaterialParams> {}

export interface CreateBomParams {
  bom_id: string
  clothing_id: string
  bom_year: string
  version?: string
  bom_items?: BomItem[]
  status?: string
  remark?: string
}

export interface UpdateBomParams extends Partial<CreateBomParams> {}

export interface CreateMaterialCategoryParams {
  category_id: string
  category_name: string
  parent_category_id?: string
  level: number
  sort_order?: number
  description?: string
  state?: string
}

export interface UpdateMaterialCategoryParams extends Partial<CreateMaterialCategoryParams> {}

// 导入响应
export interface ImportResponse {
  success: boolean
  message: string
  successCount: number
  failCount: number
  errors?: string[]
}
