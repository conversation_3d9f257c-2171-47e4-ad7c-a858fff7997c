import { Injectable, HttpException, HttpStatus } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model } from 'mongoose'
import { Product } from '../../../models/product.model'
import { Package } from '../../../models/package.model'
import { OperationLog } from '../../../models/operationLog.model'
import { Warehouse } from '../../../models/warehouse.model'
import { Clothing } from '../../../models/clothing.model'
import { OemClothing } from '../../../models/oemClothing.model'


// 导入拆分后的服务
import { InboundService } from './services/inbound.service'
import { OutboundService } from './services/outbound.service'
import { TransferService } from './services/transfer.service'
import { InventoryService } from './services/inventory.service'

@Injectable()
export class WarehouseManagementService {
  constructor(
    @InjectModel('Product') private productModel: Model<Product>,
    @InjectModel('Package') private packageModel: Model<Package>,
    @InjectModel('OperationLog') private operationLogModel: Model<OperationLog>,
    @InjectModel('Warehouse') private warehouseModel: Model<Warehouse>,
    @InjectModel('Clothing') private clothingModel: Model<Clothing>,
    @InjectModel('OemClothing') private oemClothingModel: Model<OemClothing>,
    // 注入拆分后的服务
    private inboundService: InboundService,
    private outboundService: OutboundService,
    private transferService: TransferService,
    private inventoryService: InventoryService
  ) {}

  /**
   * 生成包裹分类码
   * @param contents 包裹内容数组
   * @returns 分类码字符串
   */
  private generateClassificationCode(contents: Array<{ sku: string; original_quantity: number }>): string {
    // 按SKU排序确保分类码的一致性
    const sortedContents = contents
      .map(content => `${content.sku}_${content.original_quantity}`)
      .sort()

    return sortedContents.join('_')
  }

  /**
   * 入库操作 - 委托给入库服务
   */
  async inbound(inboundData: {
    transportation_id: string
    inbound_items: Array<{
      warehouse_id: string
      clothing_id: string
      clothing_name: string
      oem: string
      out_pcs: number
      pieces_per_package: number
      inbound_pcs: number
      package_quantity: number
      supplier?: string
      transportation_id: string
      series_number: number
    }>
    operator: string
    operation_date?: string
  }) {
    return this.inboundService.inbound(inboundData)
  }

  /**
   * 获取已入库统计数据 - 委托给入库服务
   */
  async getInboundStatistics(transportation_id: string) {
    return this.inboundService.getInboundStatistics(transportation_id)
  }

  /**
   * 入库冲正操作 - 委托给入库服务
   */
  async reverseInboundOperation(reverseData: {
    package_code: string
    operator: string
    notes?: string
    operation_date?: string
  }) {
    return this.inboundService.reverseInboundOperation(reverseData)
  }

  /**
   * 出库操作 - 委托给出库服务
   */
  async outbound(outboundData: {
    warehouse_id: string
    items: Array<{
      classification_code: string
      package_count: number // 要出库的包裹数（可以是小数）
    }>
    operator: string
    notes?: string
    operation_date?: string
  }) {
    return this.outboundService.outbound(outboundData)
  }

  /**
   * 处理完整包裹出库
   */
  private async processFullPackageOutbound(
    packageDoc: any,
    outboundData: any,
    operationLogs: any[]
  ) {
    // 记录变化
    const contentsChanges = packageDoc.contents.map((content: any) => ({
      sku: content.sku,
      product_name: content.name,
      quantity_change: -content.current_quantity,
      before_quantity: content.current_quantity,
      after_quantity: 0
    }))

    // 更新包裹状态为已出库
    await this.packageModel.updateOne(
      { package_code: packageDoc.package_code },
      {
        $set: {
          status: 'shipped',
          remaining_percentage: 0,
          last_updated_at: new Date(),
          'contents.$[].current_quantity': 0
        }
      }
    )

    // 记录操作日志
    operationLogs.push({
      operation_type: 'outbound',
      operator_name: outboundData.operator,
      warehouse_id: outboundData.warehouse_id,
      package_code: packageDoc.package_code,
      contents_changes: contentsChanges,
      details: {
        classification_code: packageDoc.classification_code,
        outbound_type: 'full_package',
        notes: outboundData.notes || '完整包裹出库'
      }
    })
  }

  /**
   * 处理部分包裹出库
   */
  private async processPartialPackageOutbound(
    packageDoc: any,
    outboundPercentage: number,
    outboundData: any,
    operationLogs: any[]
  ) {
    // 计算每个contents项的出库数量
    const contentsChanges: any[] = []
    const updateOperations: any = {
      $set: {
        last_updated_at: new Date()
      }
    }

    packageDoc.contents.forEach((content: any, index: number) => {
      const outboundQuantity = content.original_quantity * outboundPercentage
      const newQuantity = Math.max(0, content.current_quantity - outboundQuantity)

      contentsChanges.push({
        sku: content.sku,
        product_name: content.name,
        quantity_change: -outboundQuantity,
        before_quantity: content.current_quantity,
        after_quantity: newQuantity
      })

      updateOperations.$set[`contents.${index}.current_quantity`] = newQuantity
    })

    // 计算新的剩余百分比
    const newRemainingPercentage = Math.max(0, packageDoc.remaining_percentage - outboundPercentage)
    const newStatus = newRemainingPercentage === 0 ? 'shipped' : 'partially_shipped'

    updateOperations.$set.status = newStatus
    updateOperations.$set.remaining_percentage = newRemainingPercentage

    // 更新包裹
    await this.packageModel.updateOne(
      { package_code: packageDoc.package_code },
      updateOperations
    )

    // 记录操作日志
    operationLogs.push({
      operation_type: 'outbound',
      operator_name: outboundData.operator,
      warehouse_id: outboundData.warehouse_id,
      package_code: packageDoc.package_code,
      contents_changes: contentsChanges,
      details: {
        classification_code: packageDoc.classification_code,
        outbound_type: 'partial_package',
        outbound_percentage: outboundPercentage,
        notes: outboundData.notes || '部分包裹出库'
      }
    })
  }

  /**
   * 批量出库操作 - 委托给出库服务
   */
  async batchOutbound(batchOutboundData: {
    warehouses: Array<{
      warehouse_id: string
      warehouse_name: string
      warehouse_address?: string
      items: Array<{
        classification_code: string
        package_count: number
        total_pieces: number
        package_type: 'single' | 'mixed'
        contents: Array<{
          sku: string
          name: string
          outbound_quantity: number
          current_quantity?: number
          original_quantity?: number
        }>
      }>
      total_packages: number
      total_pieces: number
    }>
    total_packages: number
    total_pieces: number
    operator?: string
    notes?: string
    operation_date?: string
  }) {
    return this.outboundService.batchOutbound(batchOutboundData)
  }

  /**
   * 混合包裹出库操作 - 委托给出库服务
   */
  async mixedPackageOutbound(outboundData: {
    warehouse_id: string
    items: Array<{
      classification_code: string
      package_count: number
      total_pieces: number
      contents: Array<{
        sku: string
        name: string
        outbound_quantity: number
        current_quantity?: number
        original_quantity?: number
      }>
    }>
    operator: string
    notes?: string
    operation_date?: string
  }) {
    return this.outboundService.mixedPackageOutbound(outboundData)
  }

  /**
   * 获取包含统计数据的仓库列表
   * 专门为仓库列表页面提供优化的数据结构
   */
  async getWarehousesWithStats(status: string = 'active') {
    try {
      // 1. 获取仓库基础信息
      const warehouses = await this.warehouseModel
        .find({ status })
        .sort({ address: 1, name: 1 }) // 按地址和名称排序
        .exec()

      // 2. 使用聚合管道为每个仓库计算统计数据
      const warehouseStatsPromises = warehouses.map(async (warehouse) => {
        const statsResult = await this.packageModel.aggregate([
          {
            $match: {
              warehouse_id: warehouse.warehouse_id,
              status: { $in: ['in_stock', 'partially_shipped'] }
            }
          },
          {
            $group: {
              _id: null,
              // 计算总包数：in_stock状态包裹数 + partially_shipped状态包裹的remaining_percentage总和
              total_packages: {
                $sum: {
                  $cond: [
                    { $eq: ['$status', 'in_stock'] },
                    1, // in_stock状态每个包裹算1包
                    '$remaining_percentage' // partially_shipped状态使用remaining_percentage
                  ]
                }
              },
              // 计算总件数：所有包裹contents的current_quantity总和
              total_pieces: {
                $sum: { $sum: '$contents.current_quantity' }
              },
              // 获取最近更新时间
              last_updated: { $max: '$last_updated_at' }
            }
          }
        ]).exec()

        const stats = statsResult[0] || {
          total_packages: 0,
          total_pieces: 0,
          last_updated: null
        }

        // 添加日志打印（仅对萨达沃仓库）
        if (warehouse.address && warehouse.address.includes('萨达沃')) {
          console.log('=== getWarehousesWithStats 萨达沃仓库调试信息 ===')
          console.log('仓库信息:', {
            warehouse_id: warehouse.warehouse_id,
            name: warehouse.name,
            address: warehouse.address
          })
          console.log('聚合查询原始结果:', statsResult[0])
          console.log('最终统计数据:', {
            packages: Math.round(stats.total_packages * 100) / 100,
            pieces: stats.total_pieces || 0
          })
          console.log('===============================================')
        }

        return {
          warehouse_id: warehouse.warehouse_id,
          name: warehouse.name,
          address: warehouse.address,
          region: warehouse.region,
          capacity: warehouse.capacity,
          rent_price: warehouse.rent_price,
          rent_date: warehouse.rent_date,
          return_date: warehouse.return_date,
          status: warehouse.status,
          remark: warehouse.remark,
          createTime: warehouse.createTime,
          lastChangeTime: warehouse.lastChangeTime,
          stats: {
            packages: Math.round(stats.total_packages * 100) / 100, // 保留2位小数
            pieces: stats.total_pieces || 0,
            last_updated: stats.last_updated
          }
        }
      })

      // 3. 等待所有仓库统计数据计算完成
      const warehousesWithStats = await Promise.all(warehouseStatsPromises)

      // 4. 按地址排序，然后按包数从多到少排序
      warehousesWithStats.sort((a, b) => {
        // 首先按地址排序
        if (a.address !== b.address) {
          return a.address.localeCompare(b.address)
        }
        // 地址相同时按包数从多到少排序
        const aPackages = a.stats?.packages || 0
        const bPackages = b.stats?.packages || 0
        return bPackages - aPackages
      })

      return {
        code: 200,
        data: {
          list: warehousesWithStats,
          total: warehousesWithStats.length,
          summary: {
            total_warehouses: warehousesWithStats.length,
            total_packages: warehousesWithStats.reduce((sum, w) => sum + (w.stats?.packages || 0), 0),
            total_pieces: warehousesWithStats.reduce((sum, w) => sum + (w.stats?.pieces || 0), 0)
          }
        },
        message: '获取仓库列表成功'
      }
    } catch (error: any) {
      console.error('获取仓库列表失败:', error)
      throw new HttpException(error.message || '获取仓库列表失败', HttpStatus.INTERNAL_SERVER_ERROR)
    }
  }

  /**
   * 移库操作 - 委托给移库服务
   */
  async transfer(transferData: {
    package_codes: string[]
    from_warehouse_id: string
    to_warehouse_id: string
    to_location_code?: string
    operator: string
    notes?: string
    operation_date?: string
  }) {
    return this.transferService.transfer(transferData)
  }

  /**
   * 出库冲正操作 - 委托给出库服务
   */
  async reverseOutboundOperation(reverseData: {
    package_code: string
    operator: string
    notes?: string
    operation_date?: string
    original_log_id?: string
  }) {
    return this.outboundService.reverseOutboundOperation(reverseData)
  }

  /**
   * 移库冲正操作 - 委托给移库服务
   */
  async reverseTransferOperation(reverseData: {
    package_code: string
    operator: string
    notes?: string
    operation_date?: string
  }) {
    return this.transferService.reverseTransferOperation(reverseData)
  }

  /**
   * 查找或创建产品
   */
  private async findOrCreateProduct(item: any): Promise<Product> {
    // 先尝试查找现有产品
    let product = null
    
    if (item.oem === '是' && item.clothing_id) {
      product = await this.productModel.findOne({ oem_clothing_id: item.clothing_id })
    } else if (item.clothing_id) {
      product = await this.productModel.findOne({ clothing_id: item.clothing_id })
    }

    // 如果找不到，创建新产品
    if (!product) {
      const sku = item.oem === '是' ? `OEM_${item.clothing_id}` : `CLO_${item.clothing_id}`
      
      product = await this.productModel.create({
        sku,
        name: item.clothing_name || '未知产品',
        description: '',
        unit: '件',
        specs: {},
        clothing_id: item.oem === '是' ? undefined : item.clothing_id,
        oem_clothing_id: item.oem === '是' ? item.clothing_id : undefined,
        is_oem: item.oem === '是',
        images: [],
        status: 'active'
      })
    }

    return product
  }

  /**
   * 盘存操作 - 委托给盘存服务
   */
  async inventoryCheck(inventoryData: {
    warehouse_id: string
    checks: Array<{
      contents: Array<{
        sku: string
        name: string
        original_quantity: number
        oem: string
      }>
      system_quantity: number // 账面库存数量（包数）
      actual_quantity: number // 实际库存数量（包数）
    }>
    operator: string
    notes?: string
    operation_date?: string
  }) {
    return this.inventoryService.inventoryCheck(inventoryData)
  }





  /**
   * 搜索已出库的服装（用于盘盈操作）
   */
  async searchShippedClothing(params: {
    warehouse_id: string
    clothing_name?: string
    page?: number
    limit?: number
  }) {
    try {
      const { warehouse_id, clothing_name, page = 1, limit = 20 } = params
      const skip = (page - 1) * limit

      // 构建匹配条件
      const matchStage: any = {
        warehouse_id,
        status: 'shipped'
      }

      if (clothing_name) {
        matchStage['contents.name'] = { $regex: clothing_name, $options: 'i' }
      }

      // 聚合查询，按contents分组
      const pipeline: any[] = [
        { $match: matchStage },
        {
          $group: {
            _id: {
              contents: "$contents",
              warehouse_id: "$warehouse_id"
            },
            package_count: { $sum: 1 },
            package_codes: { $push: "$package_code" },
            warehouse_id: { $first: "$warehouse_id" },
            latest_shipped_date: { $max: "$last_updated_at" }
          }
        },
        {
          $addFields: {
            contents: "$_id.contents",
            status_type: "shipped"
          }
        },
        {
          $project: {
            _id: 0,
            contents: 1,
            package_count: 1,
            package_codes: 1,
            warehouse_id: 1,
            latest_shipped_date: 1,
            status_type: 1
          }
        },
        { $sort: { latest_shipped_date: -1 } },
        { $skip: skip },
        { $limit: limit }
      ]

      const results = await this.packageModel.aggregate(pipeline).exec()

      // 获取总数
      const totalPipeline = [
        { $match: matchStage },
        {
          $group: {
            _id: {
              contents: "$contents",
              warehouse_id: "$warehouse_id"
            }
          }
        },
        { $count: "total" }
      ]

      const totalResult = await this.packageModel.aggregate(totalPipeline).exec()
      const total = totalResult[0]?.total || 0

      return {
        code: 200,
        data: {
          list: results,
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit)
        },
        message: '搜索已出库服装成功'
      }
    } catch (error: any) {
      console.error('搜索已出库服装失败:', error)
      throw new HttpException(error.message || '搜索已出库服装失败', HttpStatus.INTERNAL_SERVER_ERROR)
    }
  }

  /**
   * 获取全局库存汇总统计
   */
  async getGlobalInventorySummary() {
    try {
      // 使用聚合管道计算全局统计
      const summaryPipeline = [
        {
          $match: {
            status: { $in: ['in_stock', 'partially_shipped'] }
          }
        },
        {
          $group: {
            _id: null,
            // 计算总包数：in_stock状态包裹数 + partially_shipped状态包裹的remaining_percentage总和
            total_packages: {
              $sum: {
                $cond: [
                  { $eq: ['$status', 'in_stock'] },
                  1, // in_stock状态每个包裹算1包
                  '$remaining_percentage' // partially_shipped状态使用remaining_percentage
                ]
              }
            },
            // 计算总件数：所有包裹contents的current_quantity总和
            total_pieces: {
              $sum: { $sum: '$contents.current_quantity' }
            },
            // 获取最近更新时间
            last_updated: { $max: '$last_updated_at' }
          }
        }
      ]

      const result = await this.packageModel.aggregate(summaryPipeline).exec()

      const summary = result[0] || {
        total_packages: 0,
        total_pieces: 0,
        last_updated: null
      }

      // 格式化数据
      return {
        code: 200,
        data: {
          total_packages: Math.round(summary.total_packages * 100) / 100, // 保留2位小数
          total_pieces: summary.total_pieces || 0,
          last_updated: summary.last_updated || null
        },
        message: '获取全局库存汇总成功'
      }
    } catch (error: any) {
      console.error('获取全局库存汇总失败:', error)
      throw new HttpException(error.message || '获取全局库存汇总失败', HttpStatus.INTERNAL_SERVER_ERROR)
    }
  }

  /**
   * 查看库存 - 按classification_code分组的简化库存列表
   * 新的出库业务逻辑：按仓库分组，按classification_code分组，累计包裹数（remaining_percentage）
   */
  async getInventory(params: {
    warehouse_id?: string
    sku?: string
    product_name?: string
    page?: number
    limit?: number
  }) {
    try {
      const { warehouse_id, sku, product_name, page = 1, limit = 20 } = params
      const skip = (page - 1) * limit

      // 构建基础匹配条件
      const baseMatchStage: any = {
        status: { $in: ['in_stock', 'partially_shipped'] } // 只查询有库存的包裹
      }

      if (warehouse_id) {
        baseMatchStage.warehouse_id = warehouse_id
      }

      // 如果有SKU或产品名称筛选条件，需要在contents中查找
      if (sku || product_name) {
        const contentsMatch: any = {}
        if (sku) {
          contentsMatch['contents.sku'] = sku
        }
        if (product_name) {
          contentsMatch['contents.name'] = { $regex: product_name, $options: 'i' }
        }
        Object.assign(baseMatchStage, contentsMatch)
      }

      // 新的聚合管道：按仓库和classification_code分组
      const pipeline: any[] = [
        {
          $match: baseMatchStage
        },
        {
          $group: {
            _id: {
              warehouse_id: "$warehouse_id",
              classification_code: "$classification_code"
            },
            // 累计包裹数：in_stock状态的包裹按1计算，partially_shipped状态的包裹按remaining_percentage计算
            package_count: {
              $sum: {
                $cond: {
                  if: { $eq: ["$status", "in_stock"] },
                  then: 1,
                  else: "$remaining_percentage"
                }
              }
            },
            // 收集包裹信息用于前端展示
            package_codes: { $push: "$package_code" },
            contents: { $first: "$contents" }, // 取第一个包裹的contents作为代表
            package_type: { $first: "$package_type" }, // 取第一个包裹的类型作为代表
            locations: { $addToSet: "$location_code" },
            inbound_dates: { $push: "$inbound_at" },
            suppliers: { $addToSet: "$supplier" },
            transportation_ids: { $addToSet: "$transportation_id" },
            series_numbers: { $addToSet: "$series_number" },
            // 计算总件数：累加所有包裹中contents的current_quantity
            total_quantity: { $sum: { $sum: "$contents.current_quantity" } },
            // 统计不同状态的包裹数量
            in_stock_count: {
              $sum: {
                $cond: { if: { $eq: ["$status", "in_stock"] }, then: 1, else: 0 }
              }
            },
            partially_shipped_count: {
              $sum: {
                $cond: { if: { $eq: ["$status", "partially_shipped"] }, then: 1, else: 0 }
              }
            }
          }
        },
        {
          $addFields: {
            warehouse_id: "$_id.warehouse_id",
            classification_code: "$_id.classification_code"
          }
        },
        {
          $project: {
            _id: 0,
            warehouse_id: 1,
            classification_code: 1,
            package_count: { $round: ["$package_count", 2] }, // 保留2位小数
            total_quantity: { $round: ["$total_quantity", 0] }, // 件数取整
            contents: 1,
            package_type: 1,
            package_codes: 1,
            locations: 1,
            inbound_dates: 1,
            suppliers: 1,
            transportation_ids: 1,
            series_numbers: 1,
            in_stock_count: 1,
            partially_shipped_count: 1
          }
        },
        {
          $sort: {
            warehouse_id: 1,
            package_count: -1 // 按包裹数量降序排列
          }
        }
      ]

      // 执行聚合查询
      const results = await this.packageModel.aggregate(pipeline).exec()

      // 手动分页
      const total = results.length
      const paginatedInventory = results.slice(skip, skip + limit)

      // 计算汇总数据
      const totalPackages = results.reduce((sum, item) => sum + item.package_count, 0)
      const totalPieces = results.reduce((sum, item) => sum + item.total_quantity, 0)

      // 添加详细日志打印
      console.log('=== getInventory 方法调试信息 ===')
      console.log('查询参数:', { warehouse_id, sku, product_name, page, limit })
      console.log('聚合查询结果数量:', results.length)
      console.log('前5个结果详情:')
      results.slice(0, 5).forEach((item, index) => {
        console.log(`  ${index + 1}. 分类码: ${item.classification_code}`)
        console.log(`     包裹数: ${item.package_count}`)
        console.log(`     件数: ${item.total_quantity}`)
        console.log(`     in_stock数量: ${item.in_stock_count}`)
        console.log(`     partially_shipped数量: ${item.partially_shipped_count}`)
      })
      console.log('汇总计算:')
      console.log(`  总包数: ${totalPackages}`)
      console.log(`  总件数: ${totalPieces}`)
      console.log('================================')

      return {
        code: 200,
        data: {
          list: paginatedInventory,
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit),
          summary: {
            total_classification_codes: total,
            total_packages: totalPackages,
            total_pieces: totalPieces
          }
        },
        message: '获取库存成功'
      }
    } catch (error) {
      console.error('获取库存失败:', error)
      throw new HttpException('获取库存失败', HttpStatus.INTERNAL_SERVER_ERROR)
    }
  }

  /**
   * 获取仓库操作日志明细 - 按新逻辑重写
   */
  async getOperationLogsDetail(params: {
    warehouse_id?: string
    operation_type?: string
    clothing_name?: string
    start_date?: string
    end_date?: string
    page?: number
    limit?: number
  }) {
    try {
      const { warehouse_id, operation_type, clothing_name, start_date, end_date, page = 1, limit = 20 } = params
      const skip = (page - 1) * limit

      // 1. 首先按日期筛选，缩小范围
      const matchStage: any = {}
      if (warehouse_id) matchStage.warehouse_id = warehouse_id
      if (operation_type) matchStage.operation_type = operation_type

      // 排除冲正记录和被冲正的记录
      matchStage.is_reversal = { $ne: true }
      matchStage.is_reversed = { $ne: true }

      // 支持按服装名称查询
      if (clothing_name) {
        matchStage['contents_changes.product_name'] = { $regex: clothing_name, $options: 'i' }
      }

      // 支持按日期范围查询
      if (start_date || end_date) {
        matchStage.timestamp = {}
        if (start_date) {
          const startDate = new Date(start_date)
          startDate.setHours(0, 0, 0, 0)
          matchStage.timestamp.$gte = startDate
        }
        if (end_date) {
          const endDate = new Date(end_date)
          endDate.setHours(23, 59, 59, 999)
          matchStage.timestamp.$lte = endDate
        }
      }

      // 构建聚合管道
      const pipeline: any[] = [
        // 1. 按日期筛选
        { $match: matchStage },

        // 2. 根据 package_code 去 packages 集合查询对应的包裹
        {
          $lookup: {
            from: 'packages',
            localField: 'package_code',
            foreignField: 'package_code',
            as: 'package_info'
          }
        },

        // 3. 展开包裹信息
        { $unwind: { path: '$package_info', preserveNullAndEmptyArrays: true } },

        // 4. 计算包裹的 total_original_quantity 并添加 classification_code
        {
          $addFields: {
            total_original_quantity: {
              $sum: '$package_info.contents.original_quantity'
            },
            classification_code: '$package_info.classification_code',
            date_only: {
              $dateToString: {
                format: '%Y-%m-%d',
                date: '$timestamp'
              }
            }
          }
        },

        // 5. 展开 contents_changes 数组
        { $unwind: '$contents_changes' },

        // 6. 计算每个对象的 package_quantity_change
        {
          $addFields: {
            package_quantity_change: {
              $cond: {
                if: { $gt: ['$total_original_quantity', 0] },
                then: { $divide: ['$contents_changes.quantity_change', '$total_original_quantity'] },
                else: 0
              }
            }
          }
        },

        // 7. 聚合操作方向 - 重新组合每个日志记录
        {
          $group: {
            _id: '$_id',
            timestamp: { $first: '$timestamp' },
            operation_type: { $first: '$operation_type' },
            operator_name: { $first: '$operator_name' },
            warehouse_id: { $first: '$warehouse_id' },
            package_code: { $first: '$package_code' },
            classification_code: { $first: '$classification_code' },
            details: { $first: '$details' },
            date_only: { $first: '$date_only' },
            total_original_quantity: { $first: '$total_original_quantity' },
            contents_changes: {
              $push: {
                sku: '$contents_changes.sku',
                product_name: '$contents_changes.product_name',
                quantity_change: '$contents_changes.quantity_change',
                package_quantity_change: '$package_quantity_change',
                before_quantity: '$contents_changes.before_quantity',
                after_quantity: '$contents_changes.after_quantity'
              }
            }
          }
        },

        // 8. 排序：按日期倒序，同一日期按仓库排序
        {
          $sort: {
            date_only: -1,
            warehouse_id: 1
          }
        }
      ]

      // 执行聚合查询获取数据
      const logs = await this.operationLogModel.aggregate([
        ...pipeline,
        { $skip: skip },
        { $limit: limit }
      ]).exec()

      // 获取总数
      const totalResult = await this.operationLogModel.aggregate([
        { $match: matchStage },
        { $count: "total" }
      ]).exec()
      const total = totalResult[0]?.total || 0

      // 9. 汇总数据 - 先按仓库分类，再按操作方向汇总
      const summaryPipeline: any[] = [
        { $match: matchStage },
        {
          $lookup: {
            from: 'packages',
            localField: 'package_code',
            foreignField: 'package_code',
            as: 'package_info'
          }
        },
        { $unwind: { path: '$package_info', preserveNullAndEmptyArrays: true } },
        {
          $addFields: {
            total_original_quantity: {
              $sum: '$package_info.contents.original_quantity'
            },
            date_only: {
              $dateToString: {
                format: '%Y-%m-%d',
                date: '$timestamp'
              }
            }
          }
        },
        { $unwind: '$contents_changes' },
        {
          $addFields: {
            package_quantity_change: {
              $cond: {
                if: { $gt: ['$total_original_quantity', 0] },
                then: { $divide: ['$contents_changes.quantity_change', '$total_original_quantity'] },
                else: 0
              }
            }
          }
        },
        {
          $group: {
            _id: {
              warehouse_id: '$warehouse_id',
              operation_type: '$operation_type'
            },
            total_package_quantity_change: { $sum: '$package_quantity_change' },
            total_quantity_change: { $sum: '$contents_changes.quantity_change' },
            operation_count: { $sum: 1 }
          }
        },
        {
          $project: {
            _id: 1,
            warehouse_id: '$_id.warehouse_id',
            operation_type: '$_id.operation_type',
            total_package_change: {
              $cond: {
                if: { $in: ['$_id.operation_type', ['outbound', 'transfer_out']] },
                then: { $abs: '$total_package_quantity_change' },
                else: '$total_package_quantity_change'
              }
            },
            total_quantity_change: {
              $cond: {
                if: { $in: ['$_id.operation_type', ['outbound', 'transfer_out']] },
                then: { $abs: '$total_quantity_change' },
                else: '$total_quantity_change'
              }
            },
            operation_count: 1
          }
        },
        { $sort: { warehouse_id: 1, operation_type: 1 } }
      ]

      // 执行汇总查询
      const summaryData = await this.operationLogModel.aggregate(summaryPipeline).exec()

      // 获取所有移库操作涉及的仓库ID
      const transferWarehouseIds = new Set()
      logs.forEach((log: any) => {
        if ((log.operation_type === 'transfer_out' || log.operation_type === 'transfer_in') && log.details) {
          transferWarehouseIds.add(log.details.to_warehouse_id)
        }
      })

      // 获取所有涉及的仓库ID（包括日志记录的仓库和移库操作的目标仓库）
      const allWarehouseIds = new Set()
      logs.forEach((log: any) => {
        if (log.warehouse_id) {
          allWarehouseIds.add(log.warehouse_id)
        }
        if ((log.operation_type === 'transfer_out' || log.operation_type === 'transfer_in') && log.details) {
          if (log.details.to_warehouse_id) {
            allWarehouseIds.add(log.details.to_warehouse_id)
          }
        }
      })

      // 批量查询仓库信息
      const warehouseMap = new Map()
      if (allWarehouseIds.size > 0) {
        const warehouses = await this.warehouseModel.find({
          warehouse_id: { $in: Array.from(allWarehouseIds) }
        }).lean()
        warehouses.forEach(warehouse => {
          warehouseMap.set(warehouse.warehouse_id, warehouse.name)
        })
      }

      // 转换为前端期望的格式
      const formattedLogs = logs.map((log: any) => {
        const generateTimestampId = () => `${Date.now().toString(36)}-${Math.random().toString(36).substring(2, 10)}`;

        const baseLog: any = {
          id: log._id?.toString() || generateTimestampId(),
          type: log.operation_type,
          date: log.date_only,
          warehouse_id: log.warehouse_id,
          warehouse_name: warehouseMap.get(log.warehouse_id) || log.warehouse_id, // 添加仓库名称
          contents_changes: log.contents_changes || [],
          timestamp: log.timestamp,
          operator_name: log.operator_name,
          details: log.details,
          package_code: log.package_code,
          classification_code: log.classification_code,
          package_total_original_quantity: log.total_original_quantity || 0
        }

        // 为移库操作添加特有的信息
        if ((log.operation_type === 'transfer_out' || log.operation_type === 'transfer_in') && log.details) {
          baseLog.transfer_info = {
            from_warehouse_id: log.details.from_warehouse_id,
            to_warehouse_id: log.details.to_warehouse_id,
            to_warehouse_name: warehouseMap.get(log.details.to_warehouse_id) || log.details.to_warehouse_id,
            notes: log.details.notes
          }
        }

        return baseLog
      })

      // 格式化汇总数据
      const formattedSummary = summaryData.map((summary: any) => ({
        warehouse_id: summary.warehouse_id,
        operation_type: summary.operation_type,
        total_package_change: Math.round(summary.total_package_change * 100) / 100,
        total_quantity_change: Math.round(summary.total_quantity_change * 100) / 100,
        operation_count: summary.operation_count
      }))

      return {
        code: 200,
        data: {
          list: formattedLogs,
          warehouse_summary: formattedSummary,
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit)
        },
        message: '获取操作日志明细成功'
      }
    } catch (error) {
      console.error('获取操作日志明细失败:', error)
      throw new HttpException('获取操作日志明细失败', HttpStatus.INTERNAL_SERVER_ERROR)
    }
  }

  /**
   * 获取仓库操作日志汇总 - 按新逻辑重写
   */
  async getOperationLogsSummary(params: {
    warehouse_id?: string
    operation_type?: string
    clothing_name?: string
    start_date?: string
    end_date?: string
    page?: number
    limit?: number
  }) {
    try {
      const { warehouse_id, operation_type, clothing_name, start_date, end_date, page = 1, limit = 20 } = params
      const skip = (page - 1) * limit

      // 1. 首先按日期筛选，缩小范围
      const matchStage: any = {}
      if (warehouse_id) matchStage.warehouse_id = warehouse_id
      if (operation_type) matchStage.operation_type = operation_type

      // 排除冲正记录和被冲正的记录
      matchStage.is_reversal = { $ne: true }
      matchStage.is_reversed = { $ne: true }

      // 支持按服装名称查询
      if (clothing_name) {
        matchStage['contents_changes.product_name'] = { $regex: clothing_name, $options: 'i' }
      }

      // 支持按日期范围查询
      if (start_date || end_date) {
        matchStage.timestamp = {}
        if (start_date) {
          const startDate = new Date(start_date)
          startDate.setHours(0, 0, 0, 0)
          matchStage.timestamp.$gte = startDate
        }
        if (end_date) {
          const endDate = new Date(end_date)
          endDate.setHours(23, 59, 59, 999)
          matchStage.timestamp.$lte = endDate
        }
      }

      // 构建聚合管道
      const pipeline: any[] = [
        // 1. 按日期筛选
        { $match: matchStage },

        // 2. 根据 package_code 去 packages 集合查询对应的包裹
        {
          $lookup: {
            from: 'packages',
            localField: 'package_code',
            foreignField: 'package_code',
            as: 'package_info'
          }
        },

        // 3. 展开包裹信息
        { $unwind: { path: '$package_info', preserveNullAndEmptyArrays: true } },

        // 4. 计算包裹的 total_original_quantity
        {
          $addFields: {
            total_original_quantity: {
              $sum: '$package_info.contents.original_quantity'
            },
            date_only: {
              $dateToString: {
                format: '%Y-%m-%d',
                date: '$timestamp'
              }
            }
          }
        },

        // 5. 展开 contents_changes 数组
        { $unwind: '$contents_changes' },

        // 6. 计算每个对象的 package_quantity_change
        {
          $addFields: {
            package_quantity_change: {
              $cond: {
                if: { $gt: ['$total_original_quantity', 0] },
                then: { $divide: ['$contents_changes.quantity_change', '$total_original_quantity'] },
                else: 0
              }
            }
          }
        }
      ]

      // 7. 以 SKU 为主体聚合
      const skuAggregationPipeline = pipeline.concat([
        {
          $group: {
            _id: {
              date: '$date_only',
              operation_type: '$operation_type',
              warehouse_id: '$warehouse_id',
              sku: '$contents_changes.sku'
            },
            product_name: { $first: '$contents_changes.product_name' },
            total_package_quantity_change: { $sum: '$package_quantity_change' },
            total_quantity_change: { $sum: '$contents_changes.quantity_change' },
            first_timestamp: { $first: '$timestamp' },
            first_operator: { $first: '$operator_name' },
            operation_type: { $first: '$operation_type' },
            details: { $first: '$details' }
          }
        },
        {
          $project: {
            _id: 1,
            date: '$_id.date',
            operation_type: '$operation_type',
            warehouse_id: '$_id.warehouse_id',
            sku: '$_id.sku',
            product_name: 1,
            // 移出和出库为负数，入库和移入为正数
            total_package_change: {
              $cond: {
                if: { $in: ['$operation_type', ['outbound', 'transfer_out']] },
                then: { $abs: '$total_package_quantity_change' }, // 负数转正数显示
                else: '$total_package_quantity_change'
              }
            },
            total_quantity_change: {
              $cond: {
                if: { $in: ['$operation_type', ['outbound', 'transfer_out']] },
                then: { $abs: '$total_quantity_change' }, // 负数转正数显示
                else: '$total_quantity_change'
              }
            },
            timestamp: '$first_timestamp',
            operator_name: '$first_operator',
            details: '$details'
          }
        },
        { $sort: { date: -1, operation_type: 1 } }
      ])

      // 执行 SKU 聚合查询
      const skuAggregatedLogs = await this.operationLogModel.aggregate([
        ...skuAggregationPipeline,
        { $skip: skip },
        { $limit: limit }
      ]).exec()

      // 获取总数
      const totalPipeline = [
        ...skuAggregationPipeline.slice(0, -1), // 排除排序
        { $count: "total" }
      ]
      const totalResult = await this.operationLogModel.aggregate(totalPipeline).exec()
      const total = totalResult[0]?.total || 0

      // 8. 操作方向汇总（不按 SKU 分类）
      const operationSummaryPipeline = pipeline.concat([
        {
          $group: {
            _id: {
              date: '$date_only',
              operation_type: '$operation_type',
              warehouse_id: '$warehouse_id'
            },
            total_package_quantity_change: { $sum: '$package_quantity_change' },
            total_quantity_change: { $sum: '$contents_changes.quantity_change' },
            operation_count: { $sum: 1 }
          }
        },
        {
          $project: {
            _id: 1,
            date: '$_id.date',
            operation_type: '$_id.operation_type',
            warehouse_id: '$_id.warehouse_id',
            total_package_change: {
              $cond: {
                if: { $in: ['$_id.operation_type', ['outbound', 'transfer_out']] },
                then: { $abs: '$total_package_quantity_change' },
                else: '$total_package_quantity_change'
              }
            },
            total_quantity_change: {
              $cond: {
                if: { $in: ['$_id.operation_type', ['outbound', 'transfer_out']] },
                then: { $abs: '$total_quantity_change' },
                else: '$total_quantity_change'
              }
            },
            operation_count: 1
          }
        },
        { $sort: { date: -1, operation_type: 1 } }
      ])

      // 执行操作方向汇总查询
      const operationSummary = await this.operationLogModel.aggregate(operationSummaryPipeline).exec()

      // 获取移库操作涉及的仓库信息
      const transferWarehouseIds = new Set()
      skuAggregatedLogs.forEach((log: any) => {
        if ((log.operation_type === 'transfer_out' || log.operation_type === 'transfer_in') && log.details) {
          transferWarehouseIds.add(log.details.to_warehouse_id)
        }
      })

      // 批量查询仓库信息
      const warehouseMap = new Map()
      if (transferWarehouseIds.size > 0) {
        const warehouses = await this.warehouseModel.find({
          warehouse_id: { $in: Array.from(transferWarehouseIds) }
        }).lean()
        warehouses.forEach(warehouse => {
          warehouseMap.set(warehouse.warehouse_id, warehouse.name)
        })
      }

      // 转换为前端期望的格式
      const formattedLogs = skuAggregatedLogs.map((log: any) => {
        const generateTimestampId = () => `${Date.now().toString(36)}-${Math.random().toString(36).substring(2, 10)}`;

        const baseLog: any = {
          id: generateTimestampId(),
          type: log.operation_type,
          date: log.date,
          warehouse_id: log.warehouse_id,
          sku: log.sku,
          product_name: log.product_name,
          total_quantity_change: Math.round(log.total_quantity_change * 100) / 100,
          total_package_change: Math.round(log.total_package_change * 100) / 100,
          timestamp: log.timestamp,
          operator_name: log.operator_name
        }

        // 为移库操作添加额外信息
        if ((log.operation_type === 'transfer_out' || log.operation_type === 'transfer_in') && log.details) {
          baseLog.transfer_info = {
            from_warehouse_id: log.details.from_warehouse_id,
            to_warehouse_id: log.details.to_warehouse_id,
            to_warehouse_name: warehouseMap.get(log.details.to_warehouse_id) || log.details.to_warehouse_id,
            notes: log.details.notes
          }
        }

        return baseLog
      })

      // 格式化操作方向汇总
      const formattedOperationSummary = operationSummary.map((summary: any) => ({
        date: summary.date,
        operation_type: summary.operation_type,
        warehouse_id: summary.warehouse_id,
        total_package_change: Math.round(summary.total_package_change * 100) / 100,
        total_quantity_change: Math.round(summary.total_quantity_change * 100) / 100,
        operation_count: summary.operation_count
      }))

      return {
        code: 200,
        data: {
          list: formattedLogs,
          operation_summary: formattedOperationSummary,
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit)
        },
        message: '获取操作日志汇总成功'
      }
    } catch (error) {
      console.error('获取操作日志汇总失败:', error)
      throw new HttpException('获取操作日志汇总失败', HttpStatus.INTERNAL_SERVER_ERROR)
    }
  }



  /**
   * 记录操作日志
   */
  private async logOperation(logData: any, operationDate?: string) {
    const timestamp = operationDate ? new Date(operationDate) : new Date()
    await this.operationLogModel.create({
      timestamp,
      ...logData
    })
  }

  /**
   * 更新服装库存数据
   * @param operationType 操作类型：'inbound' | 'outbound' | 'transfer' | 'inventory_gain' | 'inventory_loss'
   * @param clothingUpdates 服装更新数据
   */
  private async updateClothingStock(
    operationType: 'inbound' | 'outbound' | 'transfer' | 'inventory_gain' | 'inventory_loss',
    clothingUpdates: Array<{
      clothing_id?: string
      oem_clothing_id?: string
      quantity: number
    }>
  ): Promise<void> {
    try {
      for (const update of clothingUpdates) {
        let stockChange = 0

        // 根据操作类型计算库存变化
        switch (operationType) {
          case 'inbound':
          case 'inventory_gain':
            stockChange = update.quantity // 入库和盘盈增加库存
            break
          case 'outbound':
          case 'inventory_loss':
            stockChange = -update.quantity // 出库和盘亏减少库存
            break
          case 'transfer':
            stockChange = 0 // 移库不改变总库存
            break
          default:
            console.warn(`未知的操作类型: ${operationType}`)
            continue
        }

        if (stockChange === 0) {
          continue // 移库操作不需要更新库存数
        }

        // 更新普通服装库存
        if (update.clothing_id) {
          await this.clothingModel
            .findOneAndUpdate(
              { clothing_id: update.clothing_id },
              { $inc: { stock_quantity: stockChange } },
              { new: true }
            )
            .exec()
          console.log(`更新服装 ${update.clothing_id} 库存数 ${stockChange > 0 ? '+' : ''}${stockChange}`)
        }

        // 更新OEM服装库存
        if (update.oem_clothing_id) {
          await this.oemClothingModel
            .findOneAndUpdate(
              { oem_clothing_id: update.oem_clothing_id },
              { $inc: { stock_quantity: stockChange } },
              { new: true }
            )
            .exec()
          console.log(`更新OEM服装 ${update.oem_clothing_id} 库存数 ${stockChange > 0 ? '+' : ''}${stockChange}`)
        }
      }

      console.log(`${operationType} 操作的服装库存更新完成`)
    } catch (error) {
      console.error('更新服装库存失败:', error)
      throw error
    }
  }

  /**
   * 冲正操作主函数
   */
  async reverseOperationLog(reverseData: { log_id: string; operator_name: string }) {
    try {
      // 1. 查找原始日志
      const originalLog = await this.operationLogModel.findById(reverseData.log_id)
      if (!originalLog) {
        throw new HttpException('日志记录不存在', HttpStatus.NOT_FOUND)
      }

      // 2. 检查是否已被冲正
      if (originalLog.is_reversed) {
        throw new HttpException('该日志已被冲正', HttpStatus.BAD_REQUEST)
      }

      // 3. 检查是否为冲正记录
      if (originalLog.is_reversal) {
        throw new HttpException('冲正记录不能再次冲正', HttpStatus.BAD_REQUEST)
      }

      console.log(`开始冲正操作，原始日志类型: ${originalLog.operation_type}, 包裹: ${originalLog.package_code}`)

      // 4. 冲正日志将由具体的冲正服务创建，这里不再重复创建
      console.log('准备执行具体的冲正逻辑')

      // 5. 标记原记录为已冲正
      const operationType = originalLog.operation_type as string

      if (operationType === 'transfer_out' || operationType === 'transfer_in') {
        // 移库操作需要标记两条相关记录为已冲正
        const packageCode = originalLog.package_code
        const timestamp = originalLog.timestamp

        // 查找同一包裹、同一时间的移库操作记录（移出和移入）
        const relatedTransferLogs = await this.operationLogModel.find({
          package_code: packageCode,
          timestamp: timestamp,
          operation_type: { $in: ['transfer_out', 'transfer_in'] },
          is_reversed: { $ne: true }
        })

        console.log(`找到 ${relatedTransferLogs.length} 条相关移库记录`)

        // 标记所有相关的移库记录为已冲正
        for (const log of relatedTransferLogs) {
          await this.operationLogModel.findByIdAndUpdate(
            log._id,
            { is_reversed: true }
          )
          console.log(`移库日志 ${log._id} (${log.operation_type}) 已标记为已冲正`)
        }
      } else {
        // 其他操作只标记当前记录
        await this.operationLogModel.findByIdAndUpdate(
          reverseData.log_id,
          { is_reversed: true }
        )
        console.log('原始日志已标记为已冲正')
      }

      // 6. 执行具体的冲正逻辑
      if (operationType === 'inbound') {
        // 入库冲正：删除包裹记录 + 重新计算服装库存
        await this.reverseInboundOperation({
          package_code: originalLog.package_code,
          operator: reverseData.operator_name,
          notes: `冲正操作：${originalLog.operation_type}`
        })
      } else if (operationType === 'outbound') {
        // 出库冲正：恢复包裹数量 + 重新计算服装库存
        await this.reverseOutboundOperation({
          package_code: originalLog.package_code,
          operator: reverseData.operator_name,
          notes: `冲正操作：${originalLog.operation_type}`,
          original_log_id: originalLog._id?.toString() || reverseData.log_id
        })
      } else if (operationType === 'transfer_out' || operationType === 'transfer_in') {
        // 移库冲正
        await this.reverseTransferOperation({
          package_code: originalLog.package_code,
          operator: reverseData.operator_name,
          notes: `冲正操作：${originalLog.operation_type}`
        })
      } else if (operationType === 'inventory_surplus' || operationType === 'inventory_deficit') {
        // 盘存冲正：委托给盘存服务处理
        await this.inventoryService.reverseInventoryOperation(originalLog, reverseData.operator_name)
      } else {
        // 其他操作冲正：只更新库存数据
        await this.updateInventoryForReversal(originalLog)
      }

      return {
        code: 200,
        message: '冲正成功',
        data: {
          operation_type: originalLog.operation_type,
          package_code: originalLog.package_code,
          transportation_id: originalLog.details?.transportation_id
        }
      }
    } catch (error: any) {
      console.error('冲正操作失败:', error)
      throw new HttpException(
        error.message || '冲正操作失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }












  /**
   * 为冲正操作更新库存（非入库操作使用）
   */
  private async updateInventoryForReversal(originalLog: any) {
    try {
      console.log('开始冲正库存更新，原始日志:', originalLog)

      // 根据原始操作类型进行反向库存更新
      for (const change of originalLog.contents_changes) {
        const sku = change.sku
        const quantityChange = -change.quantity_change // 反向操作

        console.log(`处理SKU: ${sku}, 数量变化: ${quantityChange}`)

        // 更新服装库存数据
        const isOemClothing = sku.includes('_') && sku.split('_')[0].toLowerCase() === 'oem'
        if (isOemClothing) {
          const oemClothingId = sku.split('_')[1]
          console.log(`更新OEM服装库存: ${oemClothingId}`)
          await this.oemClothingModel.findOneAndUpdate(
            { oem_clothing_id: oemClothingId },
            { $inc: { stock_quantity: quantityChange } }
          )
        } else {
          console.log(`更新普通服装库存: ${sku}`)
          await this.clothingModel.findOneAndUpdate(
            { clothing_id: sku },
            { $inc: { stock_quantity: quantityChange } }
          )
        }

        // 查找并更新包裹
        const packageDocs = await this.packageModel.find({
          warehouse_id: originalLog.warehouse_id,
          'contents.sku': sku
        })

        console.log(`找到 ${packageDocs.length} 个包裹需要更新`)

        for (const packageDoc of packageDocs) {
          const contentIndex = packageDoc.contents.findIndex(content => content.sku === sku)
          if (contentIndex !== -1) {
            const oldQuantity = packageDoc.contents[contentIndex].current_quantity
            packageDoc.contents[contentIndex].current_quantity += quantityChange

            // 确保数量不为负数
            if (packageDoc.contents[contentIndex].current_quantity < 0) {
              packageDoc.contents[contentIndex].current_quantity = 0
            }

            console.log(`包裹 ${packageDoc.package_code} 中 ${sku} 数量从 ${oldQuantity} 更新为 ${packageDoc.contents[contentIndex].current_quantity}`)
            await packageDoc.save()
          }
        }
      }

      console.log('冲正库存更新完成')
    } catch (error) {
      console.error('冲正库存更新失败:', error)
      throw error
    }
  }


}
