import { Injectable, NotFoundException } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model } from 'mongoose'
import { Material } from '../../models/material.model'
import { MaterialCategory } from '../../models/materialCategory.model'
import { CreateMaterialDto } from './dto/create-material.dto'
import { UpdateMaterialDto } from './dto/update-material.dto'
import { QueryMaterialDto } from './dto/query-material.dto'

@Injectable()
export class MaterialService {
  constructor(
    @InjectModel('Material') private readonly materialModel: Model<Material>,
    @InjectModel('MaterialCategory') private readonly materialCategoryModel: Model<MaterialCategory>,
  ) {}

  // 获取物料列表，支持分页和筛选
  async findAll(queryParams: QueryMaterialDto) {
    console.log('后端收到的查询参数：', JSON.stringify(queryParams))

    const { material_id, material_name, category_id, years, page = 1, limit = 10 } = queryParams

    const skip = (page - 1) * limit

    // 构建查询条件
    const filter: any = {}

    // 处理物料编号和名称条件
    if (material_id) {
      filter.material_id = { $regex: material_id, $options: 'i' }
    }

    if (material_name) {
      filter.material_name = { $regex: material_name, $options: 'i' }
    }

    if (category_id) {
      filter.category_id = category_id
    }

    // 处理年份条件（从年度价格配置中查询）
    if (years && years.length > 0) {
      filter['yearly_prices.year'] = { $in: years }
    }

    console.log('构建的查询条件：', JSON.stringify(filter))

    // 执行查询
    const total = await this.materialModel.countDocuments(filter).exec()
    const materials = await this.materialModel
      .find(filter)
      .sort({ material_id: 1 }) // 按物料编号升序排序
      .skip(skip)
      .limit(limit)
      .exec()

    // 获取所有分类信息
    const categories = await this.materialCategoryModel.find({}).exec()
    const categoryMap = new Map()
    categories.forEach(cat => {
      categoryMap.set(cat.category_id, cat.category_name)
    })

    // 为每个物料添加分类名称
    const enrichedMaterials = materials.map(material => {
      const materialObj = material.toObject()
      return {
        ...materialObj,
        category_name: categoryMap.get(material.category_id) || material.category_id
      }
    })

    console.log(`查询结果：找到 ${total} 条记录`)
    return {
      total,
      page: Number(page),
      limit: Number(limit),
      materialList: enrichedMaterials,
    }
  }

  // 获取单个物料
  async findOne(id: string): Promise<Material> {
    // 如果 Id 的开头是 WL，说明是物料编码
    if (id.startsWith('WL')) {
      const material = await this.materialModel.findOne({ material_id: id }).exec()
      if (!material) {
        throw new NotFoundException(`物料编码 ${id} 不存在`)
      }
      return material
    }
    // 如果 Id 的开头不是 WL，说明是物料 ID
    const material = await this.materialModel.findById(id).exec()
    if (!material) {
      throw new NotFoundException(`物料ID ${id} 不存在`)
    }
    return material
  }

  // 创建物料
  async create(createMaterialDto: CreateMaterialDto): Promise<Material> {
    const createdMaterial = new this.materialModel(createMaterialDto)
    return createdMaterial.save()
  }

  // 更新物料
  async update(id: string, updateMaterialDto: UpdateMaterialDto): Promise<Material> {
    // 设置最后修改时间
    const updateData = {
      ...updateMaterialDto,
      lastChangeTime: new Date()
    }

    const updatedMaterial = await this.materialModel
      .findByIdAndUpdate(id, updateData, { new: true })
      .exec()

    if (!updatedMaterial) {
      throw new NotFoundException(`物料ID ${id} 不存在`)
    }

    return updatedMaterial
  }

  // 删除物料
  async remove(id: string): Promise<Material> {
    const deletedMaterial = await this.materialModel.findByIdAndDelete(id).exec()

    if (!deletedMaterial) {
      throw new NotFoundException(`物料ID ${id} 不存在`)
    }

    return deletedMaterial
  }

  // 获取年份选项
  async getYearOptions() {
    const result = await this.materialModel.distinct('yearly_prices.year').exec()
    // 结果排序，倒序
    const stringResult = result as string[]
    stringResult.sort((a, b) => b.localeCompare(a))

    return {
      data: {
        data: stringResult,
        total: stringResult.length,
      },
    }
  }

  // 获取单位选项
  async getUnitOptions() {
    const result = await this.materialModel.distinct('unit').exec()
    const stringResult = result as string[]
    stringResult.sort()

    return {
      data: {
        data: stringResult,
        total: stringResult.length,
      },
    }
  }

  // 批量导入物料数据
  async importBatch(data: any[]) {
    const results = {
      success: 0,
      failed: 0,
      errors: [] as string[],
    }

    // 处理每一条数据
    for (const item of data) {
      try {
        // 处理数据，确保字段名称正确
        const processedItem = {
          material_id: item.material_id || item['物料编号'],
          material_name: item.material_name || item['物料名称'],
          category_id: item.category_id || item['分类ID'],
          specification: item.specification || item['规格型号'] || '',
          unit: item.unit || item['计量单位'],
          current_price: parseFloat(item.current_price || item['当前价格'] || 0),
          description: item.description || item['描述'] || '',
          yearly_prices: item.yearly_prices || [],
        }

        // 验证必要字段
        if (!processedItem.material_id || !processedItem.material_name) {
          throw new Error('物料编号和物料名称不能为空')
        }

        // 检查是否已存在相同编号的物料
        const existingMaterial = await this.materialModel
          .findOne({
            material_id: processedItem.material_id,
          })
          .exec()

        if (existingMaterial) {
          // 更新现有物料
          await this.materialModel
            .findByIdAndUpdate(existingMaterial._id, processedItem)
            .exec()
        } else {
          // 创建新物料
          const newMaterial = new this.materialModel(processedItem)
          await newMaterial.save()
        }

        results.success++
      } catch (error) {
        results.failed++
        const errorMessage = error instanceof Error ? error.message : '未知错误'
        results.errors.push(`第 ${results.success + results.failed} 行: ${errorMessage}`)
      }
    }

    return {
      data: {
        message: `导入完成，成功 ${results.success} 条，失败 ${results.failed} 条`,
        successCount: results.success,
        failCount: results.failed,
        errors: results.errors,
      },
    }
  }
}
