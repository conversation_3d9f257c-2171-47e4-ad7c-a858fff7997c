import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Query,
  Put,
} from '@nestjs/common'
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger'
import { BomService } from './bom.service'
import { CreateBomDto } from './dto/create-bom.dto'
import { UpdateBomDto } from './dto/update-bom.dto'
import { QueryBomDto } from './dto/query-bom.dto'

@ApiTags('物料清单管理')
@Controller('bom')
export class BomController {
  constructor(private readonly bomService: BomService) {}

  @Post()
  @ApiOperation({ summary: '创建物料清单' })
  @ApiResponse({ status: 201, description: '物料清单创建成功' })
  create(@Body() createBomDto: CreateBomDto) {
    return this.bomService.create(createBomDto)
  }

  @Post('save-or-update')
  @ApiOperation({ summary: '保存物料清单（存在则更新，不存在则创建）' })
  @ApiResponse({ status: 200, description: '物料清单保存成功' })
  saveOrUpdate(@Body() bomData: CreateBomDto) {
    return this.bomService.saveOrUpdate(bomData)
  }

  @Get()
  @ApiOperation({ summary: '获取物料清单列表' })
  @ApiResponse({ status: 200, description: '获取物料清单列表成功' })
  findAll(@Query() queryParams: QueryBomDto) {
    return this.bomService.findAll(queryParams)
  }

  @Get('options/years')
  @ApiOperation({ summary: '获取年份选项' })
  @ApiResponse({ status: 200, description: '获取年份选项成功' })
  getYearOptions() {
    return this.bomService.getYearOptions()
  }

  @Get('clothing/:clothingId')
  @ApiOperation({ summary: '根据服装ID获取物料清单' })
  @ApiResponse({ status: 200, description: '获取物料清单成功' })
  findByClothingId(@Param('clothingId') clothingId: string, @Query('year') year?: string) {
    return this.bomService.findByClothingId(clothingId, year)
  }

  @Post(':id/calculate-cost')
  @ApiOperation({ summary: '计算物料清单成本' })
  @ApiResponse({ status: 200, description: '成本计算成功' })
  calculateCost(@Param('id') id: string, @Body() body: { year: string }) {
    return this.bomService.calculateCost(id, body.year)
  }

  @Post(':id/copy')
  @ApiOperation({ summary: '复制物料清单' })
  @ApiResponse({ status: 201, description: '复制成功' })
  copyBom(
    @Param('id') id: string,
    @Body() body: { targetClothingId: string; targetYear: string }
  ) {
    return this.bomService.copyBom(id, body.targetClothingId, body.targetYear)
  }

  @Get(':id')
  @ApiOperation({ summary: '获取物料清单详情' })
  @ApiResponse({ status: 200, description: '获取物料清单详情成功' })
  findOne(@Param('id') id: string) {
    return this.bomService.findOne(id)
  }

  @Put(':id')
  @ApiOperation({ summary: '更新物料清单' })
  @ApiResponse({ status: 200, description: '物料清单更新成功' })
  update(@Param('id') id: string, @Body() updateBomDto: UpdateBomDto) {
    return this.bomService.update(id, updateBomDto)
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除物料清单' })
  @ApiResponse({ status: 200, description: '物料清单删除成功' })
  remove(@Param('id') id: string) {
    return this.bomService.remove(id)
  }
}
