<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    :width="activeTab === 'basic' ? '1000px' : '1200px'"
    :close-on-click-modal="false"
    @closed="resetForm"
    center
    class="clothing-dialog"
  >
    <el-tabs v-model="activeTab" class="dialog-tabs">
      <!-- 基本信息选项卡 -->
      <el-tab-pane label="基本信息" name="basic">
        <el-form ref="formRef" :model="form" :rules="rules" label-width="100px" class="clothing-form">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="所属年份" prop="clothing_year">
            <el-select v-model="form.clothing_year" placeholder="选择年份" style="width: 100%">
              <el-option v-for="year in yearOptions" :key="year" :label="year" :value="year" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="裁剪年份" prop="clipping_year">
            <el-select v-model="form.clipping_year" placeholder="选择裁剪年份" style="width: 100%">
              <el-option v-for="year in yearOptions" :key="year" :label="year" :value="year" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="缝制年份" prop="make_year">
            <el-select v-model="form.make_year" placeholder="选择缝制年份" style="width: 100%">
              <el-option v-for="year in yearOptions" :key="year" :label="year" :value="year" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="服装编号" prop="clothing_id">
            <el-input v-model="form.clothing_id" placeholder="请输入服装编号" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="服装名称" prop="clothing_name">
            <el-input v-model="form.clothing_name" placeholder="请输入服装名称" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="袖长" prop="long_or_short_sleeve">
            <el-select
              v-model="form.long_or_short_sleeve"
              placeholder="选择袖长"
              style="width: 100%"
              filterable
              allow-create
              default-first-option
            >
              <el-option v-for="item in sleeveOptions" :key="item" :label="item" :value="item" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="尺码" prop="size">
            <el-select
              v-model="form.size"
              placeholder="选择尺码"
              style="width: 100%"
              filterable
              allow-create
              default-first-option
            >
              <el-option v-for="item in sizeOptions" :key="item" :label="item" :value="item" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="款式" prop="style">
            <el-select
              v-model="form.style"
              placeholder="选择款式"
              style="width: 100%"
              filterable
              allow-create
              default-first-option
            >
              <el-option v-for="item in styleOptions" :key="item" :label="item" :value="item" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="口袋类型" prop="pocket_type">
            <el-select
              v-model="form.pocket_type"
              placeholder="选择口袋类型"
              style="width: 100%"
              filterable
              allow-create
              default-first-option
            >
              <el-option
                v-for="item in pocketTypeOptions"
                :key="item"
                :label="item"
                :value="item"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="面料组" prop="fabric_group_id">
            <el-input v-model="form.fabric_group_id" placeholder="请输入面料组" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="订单数量" prop="order_quantity">
            <el-input v-model="form.order_quantity" placeholder="请输入订单数量" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="裁剪件数" prop="clipping_pcs">
            <el-input v-model="form.clipping_pcs" placeholder="请输入裁剪件数" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="单件用料" prop="fabric_usage_per_clothing">
            <el-input v-model="form.fabric_usage_per_clothing" placeholder="请输入单件用料" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="印花" prop="printed">
            <el-input v-model="form.printed" placeholder="请输入印花信息" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="出货数" prop="shipments">
            <el-input v-model="form.shipments" placeholder="请输入出货数" disabled />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="状态" prop="state">
            <el-select v-model="form.state" placeholder="选择状态" style="width: 100%">
              <el-option label="未完成" value="0" />
              <el-option label="已完成" value="1" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item label="备注" prop="memo">
            <el-input v-model="form.memo" type="textarea" :rows="1" placeholder="请输入备注信息" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="图片" prop="img">
            <div class="image-container">
              <!-- 左侧：图片预览组件 -->
              <div class="image-preview-section">
                <div v-if="fileList.length > 0" class="image-preview-container">
                  <el-image
                    v-for="(file, index) in fileList"
                    :key="index"
                    :src="file.url"
                    fit="contain"
                    :preview-src-list="getPreviewSrcList()"
                    :initial-index="index"
                    class="preview-image-item"
                    :z-index="3000"
                  >
                    <template #error>
                      <div class="image-error">
                        <el-icon><Picture /></el-icon>
                        <div class="error-text">加载失败</div>
                      </div>
                    </template>
                  </el-image>
                </div>
              </div>

              <!-- 右侧：上传组件 - 只显示文件名 -->
              <div class="upload-section">
                <el-upload
                  class="file-upload"
                  action="#"
                  :auto-upload="false"
                  :file-list="fileList"
                  :on-change="handleFileChange"
                  :on-remove="handleFileRemove"
                  :show-file-list="true"
                  list-type="text"
                >
                  <el-button type="primary">
                    <el-icon><Plus /></el-icon>
                    选择图片
                  </el-button>
                </el-upload>
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
        </el-form>
      </el-tab-pane>

      <!-- 物料清单选项卡 -->
      <el-tab-pane label="物料清单" name="bom">
        <BomManagement
          v-if="activeTab === 'bom' && form.clothing_id"
          :clothing-id="form.clothing_id"
          :clothing-year="form.clothing_year || ''"
          :clothing-name="form.clothing_name || ''"
        />
        <div v-else-if="activeTab === 'bom'" class="empty-bom-message">
          <el-empty description="请先保存基本信息，然后再管理物料清单" />
        </div>
      </el-tab-pane>
    </el-tabs>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button v-if="activeTab === 'basic'" type="primary" @click="submitForm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Picture } from '@element-plus/icons-vue'
import type { FormInstance, FormRules, UploadFile, UploadUserFile } from 'element-plus'
import {
  createClothing,
  updateClothing,
  getClothingYearOptions,
  getFourOptions,
} from '@/api/clothing'
import type { Clothing } from '@/types/clothing'
import { uploadImage, deleteImages, UploadType } from '@/utils/upload'
import BomManagement from './BomManagement.vue'

// 定义属性
const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true,
  },
  title: {
    type: String,
    default: '新增服装',
  },
  clothing: {
    type: Object as () => Partial<Clothing>,
    default: () => ({}),
  },
})

// 定义事件
const emit = defineEmits(['update:modelValue', 'success'])

// 表单引用
const formRef = ref<FormInstance>()

// 对话框可见性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => {
    // 如果是关闭对话框，不需要在这里处理清理逻辑
    // 因为会触发@closed事件，在resetForm中处理
    emit('update:modelValue', val)
  },
})

// 选项数据
const yearOptions = ref<string[]>([])
const sleeveOptions = ref<string[]>([])
const sizeOptions = ref<string[]>([])
const styleOptions = ref<string[]>([])
const pocketTypeOptions = ref<string[]>([])

// 当前激活的选项卡
const activeTab = ref('basic')

// 表单数据
const form = reactive<Partial<Clothing>>({
  clothing_year: '',
  clothing_id: '',
  clothing_name: '',
  long_or_short_sleeve: '',
  size: '',
  style: '',
  pocket_type: '',
  fabric_group_id: '',
  order_quantity: undefined,
  clipping_pcs: undefined,
  fabric_usage_per_clothing: undefined,
  printed: '',
  shipments: undefined,
  state: '0',
  memo: '',
  img: [],
})

// 文件列表
const fileList = ref<UploadUserFile[]>([])

// 待上传的文件列表
const pendingUploadFiles = ref<File[]>([])

// 需要从服务器删除的图片
const imagesToDelete = ref<{ Key: string }[]>([])

// 表单验证规则
const rules = reactive<FormRules>({
  clothing_year: [{ required: true, message: '请选择所属年份', trigger: 'change' }],
  clothing_id: [{ required: true, message: '请输入服装编号', trigger: 'blur' }],
  clothing_name: [{ required: true, message: '请输入服装名称', trigger: 'blur' }],
})

// 监听服装数据变化
watch(
  () => props.clothing,
  (newVal) => {
    if (newVal) {
      // 复制对象，避免直接修改props
      Object.keys(form).forEach((key) => {
        // @ts-ignore
        form[key] = newVal[key] !== undefined ? newVal[key] : form[key]
      })

      // 处理图片
      fileList.value = []
      if (newVal.img && newVal.img.length > 0) {
        fileList.value = newVal.img.map(
          (img) =>
            ({
              name: img.Key.split('/').pop() || 'image.jpg',
              url: img.url,
              // 使用自定义属性存储原始图片信息
              uid: Math.random(), // 添加必要的uid属性
              customData: img, // 使用自定义属性存储原始数据
            }) as UploadUserFile
        )
      }
    }
  },
  { deep: true, immediate: true }
)

// 初始化
onMounted(async () => {
  await Promise.all([loadYearOptions(), loadFourOptions()])
})

// 加载年份选项
const loadYearOptions = async () => {
  try {
    const response = await getClothingYearOptions()
    // 检查响应格式
    if (response && response.data && Array.isArray(response.data)) {
      yearOptions.value = response.data || []
    } else {
      console.error('响应格式不正确', response)
      yearOptions.value = []
    }
  } catch (error: any) {
    console.error('加载年份选项失败', error)
    ElMessage.error(error.response?.data?.message || '加载年份选项失败')
    yearOptions.value = []
  }
}

// 加载袖长、尺码、款式、口袋类型选项，将 4 个 api 合并成一个
const loadFourOptions = async () => {
  try {
    const response = await getFourOptions()
    console.log('加载到的袖长、尺码、款式、口袋类型选项：', response)
    if (response && response.data) {
      sleeveOptions.value = (response.data as any).sleeves || []
      sizeOptions.value = (response.data as any).sizes || []
      styleOptions.value = (response.data as any).styles || []
      pocketTypeOptions.value = (response.data as any).pocketTypes || []
    } else {
      console.error('响应格式不正确', response)
      sleeveOptions.value = []
      sizeOptions.value = []
      styleOptions.value = []
      pocketTypeOptions.value = []
    }
  } catch (error: any) {
    console.error('加载袖长、尺码、款式、口袋类型选项失败', error)
    ElMessage.error(error.response?.data?.message || '加载袖长、尺码、款式、口袋类型选项失败')
    sleeveOptions.value = []
    sizeOptions.value = []
    styleOptions.value = []
    pocketTypeOptions.value = []
  }
}

// 文件变更
const handleFileChange = (uploadFile: UploadFile) => {
  console.log('文件变更：', uploadFile)

  if (uploadFile.raw) {
    // 将文件添加到待上传列表
    pendingUploadFiles.value.push(uploadFile.raw)

    // 创建临时预览
    const reader = new FileReader()
    reader.onload = (e) => {
      const newFile = {
        name: uploadFile.name,
        url: e.target?.result as string,
        uid: uploadFile.uid,
        status: 'ready',
        raw: uploadFile.raw,
      } as UploadUserFile

      // 更新文件列表
      const index = fileList.value.findIndex((file) => file.uid === uploadFile.uid)
      if (index !== -1) {
        fileList.value[index] = newFile
      } else {
        fileList.value.push(newFile)
      }
    }
    reader.readAsDataURL(uploadFile.raw)
  }
}

// 文件移除
const handleFileRemove = (uploadFile: UploadFile) => {
  console.log('文件移除：', uploadFile)

  // 如果是已有的图片（有customData），标记为需要删除
  if ((uploadFile as any).customData) {
    // 将图片添加到待删除列表，在提交时处理
    const customData = (uploadFile as any).customData
    imagesToDelete.value.push({ Key: customData.Key })
    console.log('标记图片需要删除:', customData.Key)
  } else if (uploadFile.raw) {
    // 如果是待上传的图片，从待上传列表中移除
    const index = pendingUploadFiles.value.findIndex((file) => file === uploadFile.raw)
    if (index !== -1) {
      pendingUploadFiles.value.splice(index, 1)
    }
  }

  // 从文件列表中移除
  const index = fileList.value.findIndex((file) => file.uid === uploadFile.uid)
  if (index !== -1) {
    fileList.value.splice(index, 1)
  }
}

// 获取所有图片的URL列表，用于预览
const getPreviewSrcList = () => {
  return fileList.value.map((file) => file.url || '').filter((url) => url)
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }

  // 重置表单数据
  Object.keys(form).forEach((key) => {
    // @ts-ignore
    form[key] = ''
  })
  form.state = '0'

  // 清空文件列表
  fileList.value = []

  // 清空待上传文件列表
  pendingUploadFiles.value = []

  // 清空待删除图片列表
  imagesToDelete.value = []
}

// 处理取消按钮点击
const handleCancel = () => {
  // 直接关闭弹窗，resetForm会在@closed事件中被调用
  dialogVisible.value = false
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (valid) {
      try {
        // 转换数字字段
        const submitData = { ...form } as any
        if (typeof submitData.order_quantity === 'string') {
          submitData.order_quantity = Number(submitData.order_quantity)
        }
        if (typeof submitData.clipping_pcs === 'string') {
          submitData.clipping_pcs = Number(submitData.clipping_pcs)
        }
        if (typeof submitData.fabric_usage_per_clothing === 'string') {
          submitData.fabric_usage_per_clothing = Number(submitData.fabric_usage_per_clothing)
        }
        if (typeof submitData.shipments === 'string') {
          submitData.shipments = Number(submitData.shipments)
        }

        // 处理图片数据

        // 1. 删除标记为需要删除的图片
        if (imagesToDelete.value.length > 0) {
          try {
            console.log('删除服务器上的图片:', imagesToDelete.value)
            await deleteImages(imagesToDelete.value)
            ElMessage.success('已删除标记的图片')
          } catch (error) {
            console.error('删除图片失败:', error)
            ElMessage.error('部分图片删除失败，但会继续保存表单')
          }
        }

        // 2. 获取已有的图片（从服务器加载的，且未被标记删除）
        const existingImages = fileList.value
          .filter((file) => (file as any).customData)
          .map((file) => (file as any).customData)

        // 3. 上传新选择的图片
        const newImages: any[] = []
        if (pendingUploadFiles.value.length > 0) {
          ElMessage.info('正在上传图片，请稍候...')

          // 逐个上传图片
          for (const file of pendingUploadFiles.value) {
            try {
              const imageInfo = await uploadImage(file, UploadType.CLOTHING)
              if (imageInfo) {
                newImages.push(imageInfo)
              }
            } catch (error) {
              console.error('上传图片失败:', error)
              ElMessage.error(`图片 ${file.name} 上传失败`)
              // 继续上传其他图片
            }
          }
        }

        // 4. 合并已有图片和新上传的图片
        submitData.img = [...existingImages, ...newImages]

        let response
        if (props.clothing._id) {
          // 更新
          response = await updateClothing(props.clothing._id, submitData)
          if (response && response.data) {
            ElMessage.success('更新服装成功')
            dialogVisible.value = false
            emit('success')
          } else {
            ElMessage.error(response?.data?.message || '更新服装失败')
          }
        } else {
          // 新增
          response = await createClothing(submitData)
          if (response && response.data) {
            ElMessage.success('新增服装成功')
            dialogVisible.value = false
            emit('success')
          } else {
            ElMessage.error(response?.data?.message || '新增服装失败')
          }
        }
      } catch (error: any) {
        console.error('保存服装失败', error)
        ElMessage.error(error.response?.data?.message || '保存服装失败')
      }
    } else {
      ElMessage.warning('请填写必填项')
    }
  } catch (error) {
    ElMessage.warning('请填写必填项')
  }
}
</script>

<style scoped>
.clothing-dialog :deep(.el-dialog__header) {
  padding-bottom: 10px;
  margin-bottom: 0;
  border-bottom: 1px solid #ebeef5;
}

.dialog-tabs {
  margin-bottom: 15px;
}

.dialog-tabs :deep(.el-tabs__header) {
  margin-bottom: 15px;
}

.clothing-form {
  max-height: 500px;
  overflow-y: auto;
  padding-right: 10px;
}

.empty-bom-message {
  padding: 40px 20px;
  text-align: center;
}

.dialog-footer {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}

.image-container {
  display: flex;
  gap: 20px;
}

.image-preview-section {
  flex: 3;
}

.upload-section {
  flex: 2;
  min-width: 200px;
}

.image-preview-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.preview-image-item {
  width: 100px;
  height: 100px;
  border-radius: 4px;
  border: 1px solid #ebeef5;
  cursor: pointer;
  object-fit: cover;
}

.image-error {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background-color: #f5f7fa;
  color: #909399;
}

.error-text {
  margin-top: 8px;
  font-size: 12px;
}

/* 覆盖 Element Plus 的图片预览样式 */
:deep(.el-image-viewer__wrapper) {
  z-index: 3000 !important;
}

:deep(.el-image-viewer__close) {
  color: #fff;
}

:deep(.el-image-viewer__actions) {
  opacity: 1;
}

:deep(.el-image-viewer__canvas) {
  user-select: none;
}
</style>
