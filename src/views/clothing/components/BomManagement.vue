<template>
  <div class="bom-management">
    <!-- 物料清单头部信息 -->
    <div class="bom-header">
      <div class="bom-info">
        <h4>{{ clothingName }} - 物料清单</h4>
        <p class="bom-meta">服装编号：{{ clothingId }} | 年份：{{ clothingYear }}</p>
      </div>
      <div class="bom-actions">
        <el-button
          type="primary"
          @click="handleAddItem"
        >
          添加物料
        </el-button>
        <el-button
          type="success"
          @click="handleCalculateCost"
          :disabled="!bomId"
        >
          计算成本
        </el-button>
        <el-button
          type="info"
          @click="handleCopyBom"
          :disabled="!bomId"
        >
          复制清单
        </el-button>
      </div>
    </div>

    <!-- 物料清单表格 -->
    <div class="bom-table-container">
      <el-table
        :data="bomItems"
        border
        size="small"
        max-height="400"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop="material_id" label="物料编号" width="120" align="center" />
        <el-table-column prop="material_name" label="物料名称" width="150" align="center" />
        <el-table-column prop="specification" label="规格型号" width="120" align="center" />
        <el-table-column prop="quantity" label="用量" width="80" align="center">
          <template #default="{ row, $index }">
            <el-input-number
              v-model="row.quantity"
              :precision="2"
              :min="0"
              size="small"
              @change="handleQuantityChange($index)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="unit" label="单位" width="80" align="center" />
        <el-table-column prop="unit_price" label="单价" width="100" align="center">
          <template #default="{ row }">
            <span>{{ row.unit_price ? `¥${row.unit_price}` : '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="total_cost" label="小计" width="100" align="center">
          <template #default="{ row }">
            <span>{{ row.total_cost ? `¥${row.total_cost.toFixed(2)}` : '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" min-width="120" align="center">
          <template #default="{ row }">
            <el-input
              v-model="row.remark"
              size="small"
              placeholder="备注"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" align="center" fixed="right">
          <template #default="{ $index }">
            <el-button
              type="danger"
              size="small"
              @click="handleRemoveItem($index)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 成本汇总 -->
    <div class="bom-summary">
      <div class="summary-item">
        <span class="summary-label">总物料成本：</span>
        <span class="summary-value">¥{{ totalCost.toFixed(2) }}</span>
      </div>
      <div class="summary-actions">
        <el-button
          type="primary"
          @click="handleSaveBom"
          :loading="saving"
          :disabled="bomItems.length === 0"
        >
          保存物料清单
        </el-button>
      </div>
    </div>

    <!-- 添加物料对话框 -->
    <MaterialSelectDialog
      v-model="materialSelectVisible"
      @confirm="handleMaterialSelect"
    />

    <!-- 复制物料清单对话框 -->
    <BomCopyDialog
      v-model="bomCopyVisible"
      :source-bom-id="bomId"
      @success="handleCopySuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import type { BomItem } from '@/types/bom'
import MaterialSelectDialog from './MaterialSelectDialog.vue'
import BomCopyDialog from './BomCopyDialog.vue'
import { getBomByClothingId, createBom, updateBom, calculateBomCost } from '@/api/bom'

// Props
interface Props {
  clothingId: string
  clothingYear: string
  clothingName: string
}

const props = defineProps<Props>()

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const materialSelectVisible = ref(false)
const bomCopyVisible = ref(false)
const selectedItems = ref<BomItem[]>([])

// 物料清单数据
const bomId = ref('')
const bomItems = ref<BomItem[]>([])
const bomVersion = ref('1.0')
const bomStatus = ref('draft')

// 计算总成本
const totalCost = computed(() => {
  return bomItems.value.reduce((sum, item) => {
    return sum + (item.total_cost || 0)
  }, 0)
})

// 加载物料清单数据
const loadBomData = async () => {
  if (!props.clothingId || !props.clothingYear) return

  try {
    loading.value = true
    const response = await getBomByClothingId(props.clothingId, props.clothingYear)

    if (response && response.data) {
      const bom = response.data as any
      bomId.value = bom.bom_id || ''
      bomItems.value = bom.bom_items || []
      bomVersion.value = bom.version || '1.0'
      bomStatus.value = bom.status || 'draft'
    } else {
      // 没有找到物料清单，初始化空数据
      bomId.value = `BOM_${props.clothingId}_${props.clothingYear}`
      bomItems.value = []
      bomVersion.value = '1.0'
      bomStatus.value = 'draft'
    }
  } catch (error) {
    console.error('加载物料清单失败:', error)
    // 初始化空数据
    bomId.value = `BOM_${props.clothingId}_${props.clothingYear}`
    bomItems.value = []
    bomVersion.value = '1.0'
    bomStatus.value = 'draft'
  } finally {
    loading.value = false
  }
}

// 监听props变化，重新加载数据
watch(
  () => [props.clothingId, props.clothingYear],
  async () => {
    if (props.clothingId && props.clothingYear) {
      await nextTick() // 确保DOM更新完成
      loadBomData()
    }
  },
  { immediate: false } // 改为false，避免初始化时的问题
)

// 组件挂载后初始化
onMounted(async () => {
  if (props.clothingId && props.clothingYear) {
    await loadBomData()
  }
})

// 添加物料项
const handleAddItem = () => {
  materialSelectVisible.value = true
}

// 选择物料回调
const handleMaterialSelect = (materials: any[]) => {
  materials.forEach(material => {
    const existingIndex = bomItems.value.findIndex(item => item.material_id === material.material_id)
    
    if (existingIndex >= 0) {
      // 如果物料已存在，增加数量
      bomItems.value[existingIndex].quantity += 1
      calculateItemCost(existingIndex)
    } else {
      // 添加新物料项
      bomItems.value.push({
        material_id: material.material_id,
        quantity: 1,
        unit: material.unit,
        unit_price: material.current_price || 0,
        total_cost: material.current_price || 0,
        remark: '',
        material_name: material.material_name,
        material_specification: material.specification,
      })
    }
  })
}

// 删除物料项
const handleRemoveItem = (index: number) => {
  bomItems.value.splice(index, 1)
}

// 数量变化处理
const handleQuantityChange = (index: number) => {
  calculateItemCost(index)
}

// 计算单项成本
const calculateItemCost = (index: number) => {
  const item = bomItems.value[index]
  item.total_cost = item.quantity * (item.unit_price || 0)
}

// 选择变化处理
const handleSelectionChange = (selection: BomItem[]) => {
  selectedItems.value = selection
}

// 计算成本
const handleCalculateCost = async () => {
  if (!bomId.value) {
    ElMessage.warning('请先保存物料清单')
    return
  }

  try {
    const response = await calculateBomCost(bomId.value, props.clothingYear)
    if (response && response.data) {
      const responseData = response.data as any
      const { item_costs } = responseData

      // 更新物料项的价格信息
      bomItems.value.forEach((item) => {
        const costInfo = item_costs.find((cost: any) => cost.material_id === item.material_id)
        if (costInfo) {
          item.unit_price = costInfo.unit_price
          item.total_cost = costInfo.total_cost
        }
      })

      ElMessage.success('成本计算完成')
    }
  } catch (error) {
    console.error('计算成本失败:', error)
    ElMessage.error('计算成本失败')
  }
}

// 复制物料清单
const handleCopyBom = () => {
  if (!bomId.value) {
    ElMessage.warning('请先保存物料清单')
    return
  }
  bomCopyVisible.value = true
}

// 复制成功回调
const handleCopySuccess = () => {
  ElMessage.success('复制成功')
}

// 保存物料清单
const handleSaveBom = async () => {
  if (bomItems.value.length === 0) {
    ElMessage.warning('请至少添加一个物料项')
    return
  }

  try {
    saving.value = true

    const bomData = {
      bom_id: bomId.value,
      clothing_id: props.clothingId,
      bom_year: props.clothingYear,
      version: bomVersion.value,
      bom_items: bomItems.value,
      total_material_cost: totalCost.value,
      status: bomStatus.value,
      remark: '',
    }

    let response
    // 检查是否已存在
    const existingBom = await getBomByClothingId(props.clothingId, props.clothingYear)

    if (existingBom && existingBom.data && (existingBom.data as any)._id) {
      // 更新
      response = await updateBom((existingBom.data as any)._id, bomData)
    } else {
      // 创建
      response = await createBom(bomData)
    }

    if (response && (response.data || (response as any).code >= 200 && (response as any).code < 300)) {
      ElMessage.success((response as any).message || '保存成功')
    } else {
      ElMessage.error('保存失败')
    }
  } catch (error: any) {
    console.error('保存物料清单失败:', error)
    ElMessage.error(error.response?.data?.message || '保存失败')
  } finally {
    saving.value = false
  }
}
</script>

<style scoped>
.bom-management {
  padding: 20px;
}

.bom-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.bom-info h4 {
  margin: 0 0 5px 0;
  color: #303133;
}

.bom-meta {
  margin: 0 0 5px 0;
  color: #909399;
  font-size: 12px;
}

.bom-table-container {
  margin-bottom: 20px;
}

.bom-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.summary-item {
  display: flex;
  align-items: center;
}

.summary-label {
  font-weight: bold;
  color: #303133;
  margin-right: 10px;
}

.summary-value {
  font-size: 18px;
  font-weight: bold;
  color: #e6a23c;
}
</style>
