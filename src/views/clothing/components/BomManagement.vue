<template>
  <div class="bom-management" :class="{ 'api-available': apiAvailable }">
    <!-- API状态提示 -->
    <div v-if="!apiAvailable" class="api-status-alert">
      <el-alert
        title="开发模式"
        type="warning"
        :closable="false"
        show-icon
      >
        <template #default>
          <p>物料清单功能正在开发中，当前使用模拟数据。完整功能将在后端集成完成后可用。</p>
        </template>
      </el-alert>
    </div>

    <!-- 物料清单头部信息 -->
    <div class="bom-header">
      <div class="bom-info">
        <h4>{{ clothingName }} - 物料清单</h4>
        <p class="bom-meta">服装编号：{{ clothingId }} | 年份：{{ clothingYear }}</p>
        <p v-if="!apiAvailable" class="dev-mode-tip">
          <el-tag type="warning" size="small">开发模式</el-tag>
        </p>
      </div>
      <div class="bom-actions">
        <el-button
          type="primary"
          @click="handleAddItem"
          :disabled="!apiAvailable"
        >
          添加物料
        </el-button>
        <el-button
          type="success"
          @click="handleCalculateCost"
          :disabled="!bomId || !apiAvailable"
        >
          计算成本
        </el-button>
        <el-button
          type="info"
          @click="handleCopyBom"
          :disabled="!bomId || !apiAvailable"
        >
          复制清单
        </el-button>
      </div>
    </div>

    <!-- 物料清单表格 -->
    <div class="bom-table-container">
      <el-table
        :data="bomItems"
        border
        size="small"
        max-height="400"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop="material_id" label="物料编号" width="120" align="center" />
        <el-table-column prop="material_name" label="物料名称" width="150" align="center" />
        <el-table-column prop="specification" label="规格型号" width="120" align="center" />
        <el-table-column prop="quantity" label="用量" width="80" align="center">
          <template #default="{ row, $index }">
            <el-input-number
              v-model="row.quantity"
              :precision="2"
              :min="0"
              size="small"
              @change="handleQuantityChange($index)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="unit" label="单位" width="80" align="center" />
        <el-table-column prop="unit_price" label="单价" width="100" align="center">
          <template #default="{ row }">
            <span>{{ row.unit_price ? `¥${row.unit_price}` : '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="total_cost" label="小计" width="100" align="center">
          <template #default="{ row }">
            <span>{{ row.total_cost ? `¥${row.total_cost.toFixed(2)}` : '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" min-width="120" align="center">
          <template #default="{ row }">
            <el-input
              v-model="row.remark"
              size="small"
              placeholder="备注"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" align="center" fixed="right">
          <template #default="{ $index }">
            <el-button
              type="danger"
              size="small"
              @click="handleRemoveItem($index)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 成本汇总 -->
    <div class="bom-summary">
      <div class="summary-item">
        <span class="summary-label">总物料成本：</span>
        <span class="summary-value">¥{{ totalCost.toFixed(2) }}</span>
      </div>
      <div class="summary-actions">
        <el-button
          type="primary"
          @click="handleSaveBom"
          :loading="saving"
          :disabled="!apiAvailable && bomItems.length === 0"
        >
          {{ apiAvailable ? '保存物料清单' : '保存物料清单 (模拟)' }}
        </el-button>
      </div>
    </div>

    <!-- 添加物料对话框 -->
    <MaterialSelectDialog
      v-model="materialSelectVisible"
      @confirm="handleMaterialSelect"
    />

    <!-- 复制物料清单对话框 -->
    <BomCopyDialog
      v-model="bomCopyVisible"
      :source-bom-id="bomId"
      @success="handleCopySuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick, defineAsyncComponent } from 'vue'
import { ElMessage } from 'element-plus'
import type { BomItem } from '@/types/bom'

// Mock API functions for development
const mockBomApi = {
  async getBomByClothingId(clothingId: string, year?: string) {
    console.log('Mock: getBomByClothingId', clothingId, year)
    return Promise.resolve({
      data: {
        bom_id: `BOM_${clothingId}_${year}`,
        clothing_id: clothingId,
        bom_year: year,
        version: '1.0',
        status: 'draft',
        bom_items: [],
        total_material_cost: 0
      }
    })
  },
  async createBom(data: any) {
    console.log('Mock: createBom', data)
    return Promise.resolve({ data: { ...data, _id: 'mock_id' } })
  },
  async updateBom(id: string, data: any) {
    console.log('Mock: updateBom', id, data)
    return Promise.resolve({ data: { ...data, _id: id } })
  },
  async calculateBomCost(bomId: string, year: string) {
    console.log('Mock: calculateBomCost', bomId, year)
    return Promise.resolve({
      data: {
        total_cost: 0,
        item_costs: []
      }
    })
  }
}

// 动态导入组件，避免依赖错误
const MaterialSelectDialog = defineAsyncComponent({
  loader: () => import('./MaterialSelectDialog.vue').catch(() => {
    console.warn('MaterialSelectDialog组件未找到，使用fallback')
    return import('./MaterialSelectDialogFallback.vue')
  }),
  errorComponent: { template: '<div>物料选择功能加载失败</div>' },
  loadingComponent: { template: '<div>加载中...</div>' }
})

const BomCopyDialog = defineAsyncComponent({
  loader: () => import('./BomCopyDialog.vue').catch(() => {
    console.warn('BomCopyDialog组件未找到，使用fallback')
    return import('./BomCopyDialogFallback.vue')
  }),
  errorComponent: { template: '<div>复制功能加载失败</div>' },
  loadingComponent: { template: '<div>加载中...</div>' }
})

// Props
interface Props {
  clothingId: string
  clothingYear: string
  clothingName: string
}

const props = defineProps<Props>()

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const materialSelectVisible = ref(false)
const bomCopyVisible = ref(false)
const selectedItems = ref<BomItem[]>([])
const apiAvailable = ref(false) // API可用性标志

// 物料清单数据
const bomId = ref('')
const bomItems = ref<BomItem[]>([])
const bomVersion = ref('1.0')
const bomStatus = ref('draft')

// 计算总成本
const totalCost = computed(() => {
  return bomItems.value.reduce((sum, item) => {
    return sum + (item.total_cost || 0)
  }, 0)
})

// 检查API可用性并获取API函数
const getApiOrMock = async () => {
  try {
    const bomApi = await import('@/api/bom')
    apiAvailable.value = true
    return bomApi
  } catch (error) {
    console.warn('BOM API不可用，使用Mock数据:', error)
    apiAvailable.value = false
    return mockBomApi
  }
}

// 加载物料清单数据
const loadBomData = async () => {
  if (!props.clothingId || !props.clothingYear) return

  try {
    loading.value = true
    const api = await getApiOrMock()
    const response = await api.getBomByClothingId(props.clothingId, props.clothingYear)

    if (response && response.data) {
      const bom = response.data as any
      bomId.value = bom.bom_id || ''
      bomItems.value = bom.bom_items || []
      bomVersion.value = bom.version || '1.0'
      bomStatus.value = bom.status || 'draft'
    } else {
      // 没有找到物料清单，初始化空数据
      bomId.value = `BOM_${props.clothingId}_${props.clothingYear}`
      bomItems.value = []
      bomVersion.value = '1.0'
      bomStatus.value = 'draft'
    }
  } catch (error) {
    console.error('加载物料清单失败:', error)
    // 初始化空数据
    bomId.value = `BOM_${props.clothingId}_${props.clothingYear}`
    bomItems.value = []
    bomVersion.value = '1.0'
    bomStatus.value = 'draft'
  } finally {
    loading.value = false
  }
}

// 监听props变化，重新加载数据
watch(
  () => [props.clothingId, props.clothingYear],
  async () => {
    if (props.clothingId && props.clothingYear) {
      await nextTick() // 确保DOM更新完成
      loadBomData()
    }
  },
  { immediate: false } // 改为false，避免初始化时的问题
)

// 组件挂载后初始化
onMounted(async () => {
  if (props.clothingId && props.clothingYear) {
    await loadBomData()
  }
})

// 添加物料项
const handleAddItem = () => {
  if (!apiAvailable.value) {
    ElMessage.info('当前为开发模式，将显示示例物料选择')
  }
  materialSelectVisible.value = true
}

// 选择物料回调
const handleMaterialSelect = (materials: any[]) => {
  materials.forEach(material => {
    const existingIndex = bomItems.value.findIndex(item => item.material_id === material.material_id)
    
    if (existingIndex >= 0) {
      // 如果物料已存在，增加数量
      bomItems.value[existingIndex].quantity += 1
      calculateItemCost(existingIndex)
    } else {
      // 添加新物料项
      bomItems.value.push({
        material_id: material.material_id,
        quantity: 1,
        unit: material.unit,
        unit_price: material.current_price || 0,
        total_cost: material.current_price || 0,
        remark: '',
        material_name: material.material_name,
        material_specification: material.specification,
      })
    }
  })
}

// 删除物料项
const handleRemoveItem = (index: number) => {
  bomItems.value.splice(index, 1)
}

// 数量变化处理
const handleQuantityChange = (index: number) => {
  calculateItemCost(index)
}

// 计算单项成本
const calculateItemCost = (index: number) => {
  const item = bomItems.value[index]
  item.total_cost = item.quantity * (item.unit_price || 0)
}

// 选择变化处理
const handleSelectionChange = (selection: BomItem[]) => {
  selectedItems.value = selection
}

// 计算成本
const handleCalculateCost = async () => {
  if (!bomId.value) {
    ElMessage.warning('请先保存物料清单')
    return
  }

  try {
    const api = await getApiOrMock()
    const response = await api.calculateBomCost(bomId.value, props.clothingYear)
    if (response && response.data) {
      const responseData = response.data as any
      const { item_costs } = responseData

      // 更新物料项的价格信息
      bomItems.value.forEach((item) => {
        const costInfo = item_costs.find((cost: any) => cost.material_id === item.material_id)
        if (costInfo) {
          item.unit_price = costInfo.unit_price
          item.total_cost = costInfo.total_cost
        }
      })

      ElMessage.success('成本计算完成')
    }
  } catch (error) {
    console.error('计算成本失败:', error)
    ElMessage.error(apiAvailable.value ? '计算成本失败' : '成本计算功能暂未可用')
  }
}

// 复制物料清单
const handleCopyBom = () => {
  if (!bomId.value) {
    ElMessage.warning('请先保存物料清单')
    return
  }
  if (!apiAvailable.value) {
    ElMessage.info('当前为开发模式，复制功能暂未可用')
  }
  bomCopyVisible.value = true
}

// 复制成功回调
const handleCopySuccess = () => {
  ElMessage.success('复制成功')
}

// 保存物料清单
const handleSaveBom = async () => {
  if (bomItems.value.length === 0) {
    ElMessage.warning('请至少添加一个物料项')
    return
  }

  try {
    saving.value = true
    const api = await getApiOrMock()

    const bomData = {
      bom_id: bomId.value,
      clothing_id: props.clothingId,
      bom_year: props.clothingYear,
      version: bomVersion.value,
      bom_items: bomItems.value,
      total_material_cost: totalCost.value,
      status: bomStatus.value,
      remark: '',
    }

    let response
    // 检查是否已存在
    const existingBom = await api.getBomByClothingId(props.clothingId, props.clothingYear)

    if (existingBom && existingBom.data && (existingBom.data as any)._id) {
      // 更新
      response = await api.updateBom((existingBom.data as any)._id, bomData)
    } else {
      // 创建
      response = await api.createBom(bomData)
    }

    if (response && (response.data || (response as any).code >= 200 && (response as any).code < 300)) {
      ElMessage.success((response as any).message || '保存成功')
    } else {
      ElMessage.error(apiAvailable.value ? '保存失败' : '保存功能暂未可用')
    }
  } catch (error: any) {
    console.error('保存物料清单失败:', error)
    ElMessage.error(error.response?.data?.message || (apiAvailable.value ? '保存失败' : '保存功能暂未可用'))
  } finally {
    saving.value = false
  }
}
</script>

<style scoped>
.bom-management {
  padding: 20px;
}

.api-status-alert {
  margin-bottom: 20px;
}

.api-status-alert :deep(.el-alert__content) {
  font-size: 13px;
}

.bom-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.bom-info h4 {
  margin: 0 0 5px 0;
  color: #303133;
}

.bom-meta {
  margin: 0 0 5px 0;
  color: #909399;
  font-size: 12px;
}

.dev-mode-tip {
  margin: 0;
}

.bom-table-container {
  margin-bottom: 20px;
}

.bom-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.summary-item {
  display: flex;
  align-items: center;
}

.summary-label {
  font-weight: bold;
  color: #303133;
  margin-right: 10px;
}

.summary-value {
  font-size: 18px;
  font-weight: bold;
  color: #e6a23c;
}

/* 开发模式下的视觉提示 */
.bom-management:not(.api-available) {
  position: relative;
}

.bom-management:not(.api-available)::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #e6a23c, #f56c6c);
  border-radius: 2px 2px 0 0;
}
</style>
