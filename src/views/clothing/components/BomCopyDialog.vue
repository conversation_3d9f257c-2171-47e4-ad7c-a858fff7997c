<template>
  <el-dialog
    v-model="dialogVisible"
    title="复制物料清单"
    width="500px"
    :close-on-click-modal="false"
    @closed="resetDialog"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="目标服装" prop="targetClothingId">
        <el-select
          v-model="form.targetClothingId"
          placeholder="选择目标服装"
          filterable
          style="width: 100%"
          @change="handleClothingChange"
        >
          <el-option
            v-for="clothing in clothingOptions"
            :key="clothing.clothing_id"
            :label="`${clothing.clothing_id} - ${clothing.clothing_name}`"
            :value="clothing.clothing_id"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="目标年份" prop="targetYear">
        <el-select
          v-model="form.targetYear"
          placeholder="选择目标年份"
          style="width: 100%"
        >
          <el-option
            v-for="year in yearOptions"
            :key="year"
            :label="year"
            :value="year"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="复制说明">
        <el-alert
          title="复制说明"
          type="info"
          :closable="false"
          show-icon
        >
          <template #default>
            <p>• 将复制当前物料清单的所有物料项到目标服装</p>
            <p>• 如果目标服装已有物料清单，将会覆盖原有数据</p>
            <p>• 复制后的物料清单状态为草稿，需要重新保存</p>
          </template>
        </el-alert>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="copying"
          @click="handleCopy"
        >
          确定复制
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { copyBom } from '@/api/bom'
import { getClothingList, getClothingYearOptions } from '@/api/clothing'
import type { Clothing } from '@/types/clothing'

// Props
interface Props {
  modelValue: boolean
  sourceBomId: string
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  success: []
}>()

// 响应式数据
const dialogVisible = ref(false)
const copying = ref(false)
const formRef = ref<FormInstance>()

// 表单数据
const form = reactive({
  targetClothingId: '',
  targetYear: '',
})

// 选项数据
const clothingOptions = ref<Clothing[]>([])
const yearOptions = ref<string[]>([])

// 表单验证规则
const rules: FormRules = {
  targetClothingId: [
    { required: true, message: '请选择目标服装', trigger: 'change' },
  ],
  targetYear: [
    { required: true, message: '请选择目标年份', trigger: 'change' },
  ],
}

// 监听对话框显示状态
watch(
  () => props.modelValue,
  (val) => {
    dialogVisible.value = val
    if (val) {
      loadOptions()
    }
  }
)

watch(dialogVisible, (val) => {
  emit('update:modelValue', val)
})

// 初始化
onMounted(() => {
  loadOptions()
})

// 加载选项数据
const loadOptions = async () => {
  await Promise.all([loadClothingOptions(), loadYearOptions()])
}

// 加载服装选项
const loadClothingOptions = async () => {
  try {
    const res = await getClothingList({ limit: 1000 })
    if (res && res.data) {
      const responseData = res.data as any
      // 处理不同的响应格式
      if (responseData.clothingList) {
        clothingOptions.value = responseData.clothingList
      } else if (Array.isArray(responseData)) {
        clothingOptions.value = responseData
      }
    }
  } catch (error) {
    console.error('加载服装选项失败:', error)
  }
}

// 加载年份选项
const loadYearOptions = async () => {
  try {
    const res = await getClothingYearOptions()
    if (res && res.data) {
      const responseData = res.data as any
      // 处理不同的响应格式
      if (responseData.data) {
        yearOptions.value = responseData.data
      } else if (Array.isArray(responseData)) {
        yearOptions.value = responseData
      }
    }
  } catch (error) {
    console.error('加载年份选项失败:', error)
  }
}

// 服装变化处理
const handleClothingChange = (clothingId: string) => {
  const selectedClothing = clothingOptions.value.find(c => c.clothing_id === clothingId)
  if (selectedClothing && selectedClothing.clothing_year) {
    form.targetYear = selectedClothing.clothing_year
  }
}

// 执行复制
const handleCopy = async () => {
  if (!formRef.value) return

  const valid = await formRef.value.validate()
  if (!valid) return

  try {
    copying.value = true
    
    const response = await copyBom(
      props.sourceBomId,
      form.targetClothingId,
      form.targetYear
    )

    if (response && (response.data || (response as any).code >= 200 && (response as any).code < 300)) {
      ElMessage.success((response as any).message || '复制成功')
      emit('success')
      dialogVisible.value = false
    } else {
      ElMessage.error('复制失败')
    }
  } catch (error: any) {
    console.error('复制物料清单失败:', error)
    ElMessage.error(error.response?.data?.message || '复制失败')
  } finally {
    copying.value = false
  }
}

// 重置对话框
const resetDialog = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  
  form.targetClothingId = ''
  form.targetYear = ''
  copying.value = false
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}

:deep(.el-alert__content) {
  font-size: 12px;
}

:deep(.el-alert__content p) {
  margin: 2px 0;
}
</style>
