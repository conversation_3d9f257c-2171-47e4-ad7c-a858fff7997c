#!/usr/bin/env node

/**
 * 物料清单(BOM)系统配置脚本
 * 此脚本检查并配置BOM系统模块
 */

const fs = require('fs')
const path = require('path')

console.log('='.repeat(70))
console.log('🔧 物料清单(BOM)系统配置脚本')
console.log('='.repeat(70))

// 当前项目路径
const PROJECT_PATH = process.cwd()
console.log(`项目路径: ${PROJECT_PATH}`)

// 检查必要的文件和目录
function checkRequiredFiles() {
  console.log('\n📁 检查必要文件和目录...')
  
  const requiredItems = [
    { path: 'src/models/material.model.ts', type: 'file', name: '物料模型' },
    { path: 'src/models/materialCategory.model.ts', type: 'file', name: '物料分类模型' },
    { path: 'src/models/billOfMaterials.model.ts', type: 'file', name: '物料清单模型' },
    { path: 'src/modules/material', type: 'dir', name: '物料管理模块' },
    { path: 'src/modules/materialCategory', type: 'dir', name: '物料分类模块' },
    { path: 'src/modules/bom', type: 'dir', name: '物料清单模块' },
    { path: 'src/app.module.ts', type: 'file', name: '应用主模块' },
    { path: 'src/models/models.module.ts', type: 'file', name: '模型模块' }
  ]
  
  let allExists = true
  requiredItems.forEach(item => {
    const fullPath = path.join(PROJECT_PATH, item.path)
    const exists = fs.existsSync(fullPath)
    
    if (exists) {
      console.log(`✅ ${item.name}: ${item.path}`)
    } else {
      console.log(`❌ ${item.name}: ${item.path} - 未找到`)
      allExists = false
    }
  })
  
  return allExists
}

// 更新app.module.ts
function updateAppModule() {
  console.log('\n⚙️  更新app.module.ts...')
  const appModulePath = path.join(PROJECT_PATH, 'src/app.module.ts')
  
  if (!fs.existsSync(appModulePath)) {
    console.log('❌ app.module.ts文件不存在')
    return false
  }

  let content = fs.readFileSync(appModulePath, 'utf8')
  
  // 需要添加的导入语句
  const imports = [
    "import { MaterialModule } from './modules/material/material.module'",
    "import { MaterialCategoryModule } from './modules/materialCategory/material-category.module'",
    "import { BomModule } from './modules/bom/bom.module'"
  ]
  
  let hasChanges = false
  
  // 检查并添加导入语句
  imports.forEach(importStatement => {
    if (!content.includes(importStatement)) {
      // 在最后一个import语句后添加
      const lastImportRegex = /import.*from.*['"][^'"]*['"];?\s*$/gm
      const matches = content.match(lastImportRegex)
      if (matches && matches.length > 0) {
        const lastImport = matches[matches.length - 1]
        const lastImportIndex = content.lastIndexOf(lastImport)
        const insertIndex = lastImportIndex + lastImport.length
        content = content.slice(0, insertIndex) + '\n' + importStatement + content.slice(insertIndex)
        hasChanges = true
        console.log(`✅ 添加导入: ${importStatement.split(' ')[3]}`)
      }
    } else {
      console.log(`✅ 导入已存在: ${importStatement.split(' ')[3]}`)
    }
  })
  
  // 检查并添加模块到imports数组
  const modules = ['MaterialModule', 'MaterialCategoryModule', 'BomModule']
  modules.forEach(module => {
    if (!content.includes(module)) {
      // 查找imports数组
      const importsMatch = content.match(/imports:\s*\[([\s\S]*?)\]/m)
      if (importsMatch) {
        const importsContent = importsMatch[1]
        const lastCommaIndex = importsContent.lastIndexOf(',')
        if (lastCommaIndex !== -1) {
          const beforeComma = importsContent.slice(0, lastCommaIndex + 1)
          const afterComma = importsContent.slice(lastCommaIndex + 1)
          const newImportsContent = beforeComma + `\n    // 物料清单系统模块\n    ${module},` + afterComma
          content = content.replace(importsMatch[0], `imports: [${newImportsContent}]`)
          hasChanges = true
          console.log(`✅ 添加模块: ${module}`)
        }
      }
    } else {
      console.log(`✅ 模块已存在: ${module}`)
    }
  })
  
  if (hasChanges) {
    fs.writeFileSync(appModulePath, content)
    console.log('✅ app.module.ts更新完成')
  } else {
    console.log('✅ app.module.ts已是最新状态')
  }
  
  return true
}

// 更新models.module.ts
function updateModelsModule() {
  console.log('\n⚙️  更新models.module.ts...')
  const modelsModulePath = path.join(PROJECT_PATH, 'src/models/models.module.ts')
  
  if (!fs.existsSync(modelsModulePath)) {
    console.log('❌ models.module.ts文件不存在')
    return false
  }

  let content = fs.readFileSync(modelsModulePath, 'utf8')
  
  // 需要添加的模式导入
  const imports = [
    "import { MaterialSchema } from './material.model'",
    "import { MaterialCategorySchema } from './materialCategory.model'",
    "import { BillOfMaterialsSchema } from './billOfMaterials.model'"
  ]
  
  let hasChanges = false
  
  // 检查并添加导入语句
  imports.forEach(importStatement => {
    if (!content.includes(importStatement)) {
      const lastImportRegex = /import.*from.*['"][^'"]*['"];?\s*$/gm
      const matches = content.match(lastImportRegex)
      if (matches && matches.length > 0) {
        const lastImport = matches[matches.length - 1]
        const lastImportIndex = content.lastIndexOf(lastImport)
        const insertIndex = lastImportIndex + lastImport.length
        content = content.slice(0, insertIndex) + '\n' + importStatement + content.slice(insertIndex)
        hasChanges = true
        console.log(`✅ 添加模式导入: ${importStatement.split(' ')[3]}`)
      }
    } else {
      console.log(`✅ 模式导入已存在: ${importStatement.split(' ')[3]}`)
    }
  })
  
  // 检查并添加模式注册
  const schemas = [
    "{ name: 'Material', schema: MaterialSchema }",
    "{ name: 'MaterialCategory', schema: MaterialCategorySchema }",
    "{ name: 'BillOfMaterials', schema: BillOfMaterialsSchema }"
  ]
  
  schemas.forEach(schema => {
    if (!content.includes(schema)) {
      const forFeatureMatch = content.match(/MongooseModule\.forFeature\(\[([\s\S]*?)\]\)/m)
      if (forFeatureMatch) {
        const schemasContent = forFeatureMatch[1]
        const lastCommaIndex = schemasContent.lastIndexOf(',')
        if (lastCommaIndex !== -1) {
          const beforeComma = schemasContent.slice(0, lastCommaIndex + 1)
          const afterComma = schemasContent.slice(lastCommaIndex + 1)
          const newSchemasContent = beforeComma + `\n      // 物料清单系统模型\n      ${schema},` + afterComma
          content = content.replace(forFeatureMatch[0], `MongooseModule.forFeature([${newSchemasContent}])`)
          hasChanges = true
          console.log(`✅ 添加模式注册: ${schema.split("'")[1]}`)
        }
      }
    } else {
      console.log(`✅ 模式注册已存在: ${schema.split("'")[1]}`)
    }
  })
  
  if (hasChanges) {
    fs.writeFileSync(modelsModulePath, content)
    console.log('✅ models.module.ts更新完成')
  } else {
    console.log('✅ models.module.ts已是最新状态')
  }
  
  return true
}

// 更新models/index.ts
function updateModelsIndex() {
  console.log('\n⚙️  更新models/index.ts...')
  const modelsIndexPath = path.join(PROJECT_PATH, 'src/models/index.ts')
  
  if (!fs.existsSync(modelsIndexPath)) {
    console.log('❌ models/index.ts文件不存在')
    return false
  }

  let content = fs.readFileSync(modelsIndexPath, 'utf8')
  
  const exports = [
    "export * from './material.model'",
    "export * from './materialCategory.model'",
    "export * from './billOfMaterials.model'"
  ]
  
  let hasChanges = false
  exports.forEach(exportStatement => {
    if (!content.includes(exportStatement)) {
      hasChanges = true
    }
  })
  
  if (hasChanges) {
    const newExports = '\n// 物料清单系统模型\n' + 
      exports.filter(exp => !content.includes(exp)).join('\n') + '\n'
    content += newExports
    fs.writeFileSync(modelsIndexPath, content)
    console.log('✅ models/index.ts更新完成')
  } else {
    console.log('✅ models/index.ts已是最新状态')
  }
  
  return true
}

// 主函数
async function main() {
  try {
    // 检查必要文件
    const filesExist = checkRequiredFiles()
    
    if (!filesExist) {
      console.log('\n❌ 缺少必要文件，请确保BOM系统文件已正确创建')
      process.exit(1)
    }
    
    // 更新配置文件
    const appModuleSuccess = updateAppModule()
    const modelsModuleSuccess = updateModelsModule()
    const modelsIndexSuccess = updateModelsIndex()
    
    // 总结
    console.log('\n' + '='.repeat(70))
    console.log('📊 配置总结')
    console.log('='.repeat(70))
    
    if (appModuleSuccess && modelsModuleSuccess && modelsIndexSuccess) {
      console.log('🎉 BOM系统配置完成!')
      console.log('\n下一步操作:')
      console.log('1. 运行数据库初始化: node scripts/init-database.js')
      console.log('2. 启动后端服务: npm run dev')
      console.log('3. 验证集成结果: node scripts/verify-integration.js')
    } else {
      console.log('⚠️  配置过程中出现问题，请检查上述日志')
    }
    
    console.log('='.repeat(70))
    
  } catch (error) {
    console.error('❌ 配置过程中发生错误:', error.message)
    process.exit(1)
  }
}

// 运行主函数
main()
