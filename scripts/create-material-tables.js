/**
 * 物料管理系统数据库表创建脚本
 * 
 * 功能：
 * 1. 创建物料分类表 (material_category)
 * 2. 创建物料信息表 (material)
 * 3. 创建物料清单表 (bill_of_materials)
 * 4. 创建必要的索引
 * 5. 插入初始数据
 * 
 * 使用方法：
 * node scripts/create-material-tables.js
 * 
 * 注意：此脚本可以重复执行，不会重复创建已存在的集合
 */

const { MongoClient } = require('mongodb')
require('dotenv').config()

// 数据库连接配置
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/JYData2025'
const DB_NAME = process.env.DB_NAME || 'JYData2025'

console.log('='.repeat(70))
console.log('物料管理系统数据库表创建脚本')
console.log('='.repeat(70))
console.log(`数据库连接: ${MONGODB_URI}`)
console.log(`数据库名称: ${DB_NAME}`)
console.log('='.repeat(70))

async function createMaterialTables() {
  let client
  
  try {
    // 连接数据库
    console.log('🔗 正在连接数据库...')
    client = new MongoClient(MONGODB_URI)
    await client.connect()
    console.log('✅ 数据库连接成功')
    
    const db = client.db(DB_NAME)
    
    // 1. 创建物料分类表 (material_category)
    await createMaterialCategoryCollection(db)
    
    // 2. 创建物料信息表 (material)
    await createMaterialCollection(db)
    
    // 3. 创建物料清单表 (bill_of_materials)
    await createBillOfMaterialsCollection(db)
    
    // 4. 创建索引
    await createIndexes(db)
    
    // 5. 插入初始数据
    await insertInitialData(db)
    
    console.log('='.repeat(70))
    console.log('🎉 物料管理系统数据库表创建完成！')
    console.log('='.repeat(70))
    
  } catch (error) {
    console.error('❌ 创建数据库表时发生错误:', error)
    process.exit(1)
  } finally {
    if (client) {
      await client.close()
      console.log('🔒 数据库连接已关闭')
    }
  }
}

/**
 * 创建物料分类表
 */
async function createMaterialCategoryCollection(db) {
  const collectionName = 'material_category'
  console.log(`\n📋 创建集合: ${collectionName}`)
  
  try {
    // 检查集合是否已存在
    const collections = await db.listCollections({ name: collectionName }).toArray()
    if (collections.length > 0) {
      console.log(`⚠️  集合 ${collectionName} 已存在，跳过创建`)
      return
    }
    
    // 创建集合并设置验证规则
    await db.createCollection(collectionName, {
      validator: {
        $jsonSchema: {
          bsonType: 'object',
          required: ['category_id', 'category_name', 'level'],
          properties: {
            category_id: {
              bsonType: 'string',
              description: '分类编号，必填且唯一'
            },
            category_name: {
              bsonType: 'string',
              description: '分类名称，必填'
            },
            parent_category_id: {
              bsonType: ['string', 'null'],
              description: '父分类ID，顶级分类为null'
            },
            level: {
              bsonType: 'int',
              minimum: 1,
              maximum: 3,
              description: '分类级别：1-主类别，2-子类别，3-具体物料项'
            },
            sort_order: {
              bsonType: ['int', 'null'],
              description: '排序顺序'
            },
            description: {
              bsonType: ['string', 'null'],
              description: '描述'
            },
            state: {
              bsonType: 'string',
              enum: ['0', '1'],
              description: '状态：1-启用，0-禁用'
            },
            createTime: {
              bsonType: 'date',
              description: '创建时间'
            },
            lastChangeTime: {
              bsonType: ['date', 'null'],
              description: '最后修改时间'
            }
          }
        }
      }
    })
    
    console.log(`✅ 集合 ${collectionName} 创建成功`)
  } catch (error) {
    console.error(`❌ 创建集合 ${collectionName} 失败:`, error.message)
    throw error
  }
}

/**
 * 创建物料信息表
 */
async function createMaterialCollection(db) {
  const collectionName = 'material'
  console.log(`\n📋 创建集合: ${collectionName}`)
  
  try {
    // 检查集合是否已存在
    const collections = await db.listCollections({ name: collectionName }).toArray()
    if (collections.length > 0) {
      console.log(`⚠️  集合 ${collectionName} 已存在，跳过创建`)
      return
    }
    
    // 创建集合并设置验证规则
    await db.createCollection(collectionName, {
      validator: {
        $jsonSchema: {
          bsonType: 'object',
          required: ['material_id', 'material_name', 'category_id', 'unit'],
          properties: {
            material_id: {
              bsonType: 'string',
              description: '物料编号，必填且唯一'
            },
            material_name: {
              bsonType: 'string',
              description: '物料名称，必填'
            },
            category_id: {
              bsonType: 'string',
              description: '分类ID，必填'
            },
            specification: {
              bsonType: ['string', 'null'],
              description: '规格型号'
            },
            unit: {
              bsonType: 'string',
              description: '计量单位，必填'
            },
            current_price: {
              bsonType: ['double', 'null'],
              minimum: 0,
              description: '当前价格'
            },
            yearly_prices: {
              bsonType: 'array',
              description: '年度价格配置',
              items: {
                bsonType: 'object',
                required: ['year', 'price'],
                properties: {
                  year: {
                    bsonType: 'string',
                    description: '年份'
                  },
                  price: {
                    bsonType: 'double',
                    minimum: 0,
                    description: '价格'
                  },
                  effective_date: {
                    bsonType: ['date', 'null'],
                    description: '生效日期'
                  },
                  supplier: {
                    bsonType: ['string', 'null'],
                    description: '供应商'
                  }
                }
              }
            },
            description: {
              bsonType: ['string', 'null'],
              description: '描述'
            },
            state: {
              bsonType: 'string',
              enum: ['0', '1'],
              description: '状态：1-启用，0-禁用'
            },
            createTime: {
              bsonType: 'date',
              description: '创建时间'
            },
            lastChangeTime: {
              bsonType: ['date', 'null'],
              description: '最后修改时间'
            }
          }
        }
      }
    })
    
    console.log(`✅ 集合 ${collectionName} 创建成功`)
  } catch (error) {
    console.error(`❌ 创建集合 ${collectionName} 失败:`, error.message)
    throw error
  }
}

/**
 * 创建物料清单表
 */
async function createBillOfMaterialsCollection(db) {
  const collectionName = 'bill_of_materials'
  console.log(`\n📋 创建集合: ${collectionName}`)
  
  try {
    // 检查集合是否已存在
    const collections = await db.listCollections({ name: collectionName }).toArray()
    if (collections.length > 0) {
      console.log(`⚠️  集合 ${collectionName} 已存在，跳过创建`)
      return
    }
    
    // 创建集合并设置验证规则
    await db.createCollection(collectionName, {
      validator: {
        $jsonSchema: {
          bsonType: 'object',
          required: ['bom_id', 'clothing_id', 'bom_year'],
          properties: {
            bom_id: {
              bsonType: 'string',
              description: 'BOM编号，必填且唯一'
            },
            clothing_id: {
              bsonType: 'string',
              description: '服装ID，必填'
            },
            bom_year: {
              bsonType: 'string',
              description: 'BOM年份，必填'
            },
            version: {
              bsonType: ['string', 'null'],
              description: '版本号'
            },
            bom_items: {
              bsonType: 'array',
              description: '物料清单项',
              items: {
                bsonType: 'object',
                required: ['material_id', 'quantity', 'unit'],
                properties: {
                  material_id: {
                    bsonType: 'string',
                    description: '物料ID'
                  },
                  quantity: {
                    bsonType: 'double',
                    minimum: 0,
                    description: '用量'
                  },
                  unit: {
                    bsonType: 'string',
                    description: '单位'
                  },
                  unit_price: {
                    bsonType: ['double', 'null'],
                    minimum: 0,
                    description: '单价'
                  },
                  total_cost: {
                    bsonType: ['double', 'null'],
                    minimum: 0,
                    description: '小计成本'
                  },
                  remark: {
                    bsonType: ['string', 'null'],
                    description: '备注'
                  }
                }
              }
            },
            total_material_cost: {
              bsonType: ['double', 'null'],
              minimum: 0,
              description: '总物料成本'
            },
            status: {
              bsonType: 'string',
              enum: ['draft', 'active', 'archived'],
              description: '状态：draft-草稿，active-生效，archived-归档'
            },
            remark: {
              bsonType: ['string', 'null'],
              description: '备注'
            },
            createTime: {
              bsonType: 'date',
              description: '创建时间'
            },
            lastChangeTime: {
              bsonType: ['date', 'null'],
              description: '最后修改时间'
            }
          }
        }
      }
    })
    
    console.log(`✅ 集合 ${collectionName} 创建成功`)
  } catch (error) {
    console.error(`❌ 创建集合 ${collectionName} 失败:`, error.message)
    throw error
  }
}

/**
 * 创建索引
 */
async function createIndexes(db) {
  console.log('\n🔍 创建索引...')

  try {
    // 物料分类表索引
    const categoryCollection = db.collection('material_category')
    await categoryCollection.createIndex({ category_id: 1 }, { unique: true, name: 'idx_category_id' })
    await categoryCollection.createIndex({ parent_category_id: 1 }, { name: 'idx_parent_category_id' })
    await categoryCollection.createIndex({ level: 1 }, { name: 'idx_level' })
    await categoryCollection.createIndex({ state: 1 }, { name: 'idx_state' })
    await categoryCollection.createIndex({ sort_order: 1 }, { name: 'idx_sort_order' })
    console.log('✅ material_category 索引创建完成')

    // 物料信息表索引
    const materialCollection = db.collection('material')
    await materialCollection.createIndex({ material_id: 1 }, { unique: true, name: 'idx_material_id' })
    await materialCollection.createIndex({ category_id: 1 }, { name: 'idx_category_id' })
    await materialCollection.createIndex({ material_name: 1 }, { name: 'idx_material_name' })
    await materialCollection.createIndex({ unit: 1 }, { name: 'idx_unit' })
    await materialCollection.createIndex({ state: 1 }, { name: 'idx_state' })
    await materialCollection.createIndex({ 'yearly_prices.year': 1 }, { name: 'idx_yearly_prices_year' })
    console.log('✅ material 索引创建完成')

    // 物料清单表索引
    const bomCollection = db.collection('bill_of_materials')
    await bomCollection.createIndex({ bom_id: 1 }, { unique: true, name: 'idx_bom_id' })
    await bomCollection.createIndex({ clothing_id: 1 }, { name: 'idx_clothing_id' })
    await bomCollection.createIndex({ bom_year: 1 }, { name: 'idx_bom_year' })
    await bomCollection.createIndex({ status: 1 }, { name: 'idx_status' })
    await bomCollection.createIndex({ clothing_id: 1, bom_year: 1 }, { name: 'idx_clothing_year' })
    await bomCollection.createIndex({ 'bom_items.material_id': 1 }, { name: 'idx_bom_items_material_id' })
    console.log('✅ bill_of_materials 索引创建完成')

    console.log('✅ 所有索引创建完成')
  } catch (error) {
    console.error('❌ 创建索引失败:', error.message)
    throw error
  }
}

/**
 * 插入初始数据
 */
async function insertInitialData(db) {
  console.log('\n📝 插入初始数据...')

  try {
    // 插入物料分类初始数据
    await insertMaterialCategoryData(db)

    // 插入物料信息初始数据
    await insertMaterialData(db)

    console.log('✅ 初始数据插入完成')
  } catch (error) {
    console.error('❌ 插入初始数据失败:', error.message)
    throw error
  }
}

/**
 * 插入物料分类初始数据
 */
async function insertMaterialCategoryData(db) {
  const collection = db.collection('material_category')

  // 检查是否已有数据
  const count = await collection.countDocuments()
  if (count > 0) {
    console.log('⚠️  material_category 已有数据，跳过初始数据插入')
    return
  }

  const initialCategories = [
    // 主类别 (level: 1)
    {
      category_id: 'CAT001',
      category_name: '面料',
      parent_category_id: null,
      level: 1,
      sort_order: 1,
      description: '各种面料材料',
      state: '1',
      createTime: new Date(),
      lastChangeTime: null
    },
    {
      category_id: 'CAT002',
      category_name: '辅料',
      parent_category_id: null,
      level: 1,
      sort_order: 2,
      description: '服装辅助材料',
      state: '1',
      createTime: new Date(),
      lastChangeTime: null
    },
    {
      category_id: 'CAT003',
      category_name: '包装材料',
      parent_category_id: null,
      level: 1,
      sort_order: 3,
      description: '产品包装相关材料',
      state: '1',
      createTime: new Date(),
      lastChangeTime: null
    },

    // 子类别 (level: 2)
    {
      category_id: 'CAT001001',
      category_name: '棉质面料',
      parent_category_id: 'CAT001',
      level: 2,
      sort_order: 1,
      description: '纯棉及棉混纺面料',
      state: '1',
      createTime: new Date(),
      lastChangeTime: null
    },
    {
      category_id: 'CAT001002',
      category_name: '化纤面料',
      parent_category_id: 'CAT001',
      level: 2,
      sort_order: 2,
      description: '聚酯纤维等化纤面料',
      state: '1',
      createTime: new Date(),
      lastChangeTime: null
    },
    {
      category_id: 'CAT002001',
      category_name: '纽扣',
      parent_category_id: 'CAT002',
      level: 2,
      sort_order: 1,
      description: '各种纽扣',
      state: '1',
      createTime: new Date(),
      lastChangeTime: null
    },
    {
      category_id: 'CAT002002',
      category_name: '拉链',
      parent_category_id: 'CAT002',
      level: 2,
      sort_order: 2,
      description: '各种拉链',
      state: '1',
      createTime: new Date(),
      lastChangeTime: null
    },
    {
      category_id: 'CAT002003',
      category_name: '线材',
      parent_category_id: 'CAT002',
      level: 2,
      sort_order: 3,
      description: '缝纫线等线材',
      state: '1',
      createTime: new Date(),
      lastChangeTime: null
    }
  ]

  await collection.insertMany(initialCategories)
  console.log(`✅ 插入 ${initialCategories.length} 条物料分类初始数据`)
}

/**
 * 插入物料信息初始数据
 */
async function insertMaterialData(db) {
  const collection = db.collection('material')

  // 检查是否已有数据
  const count = await collection.countDocuments()
  if (count > 0) {
    console.log('⚠️  material 已有数据，跳过初始数据插入')
    return
  }

  const currentYear = new Date().getFullYear().toString()

  const initialMaterials = [
    {
      material_id: 'MAT001',
      material_name: '纯棉平纹布',
      category_id: 'CAT001001',
      specification: '40支 140g/m²',
      unit: '米',
      current_price: 12.5,
      yearly_prices: [
        {
          year: currentYear,
          price: 12.5,
          effective_date: new Date(),
          supplier: '华纺集团'
        }
      ],
      description: '优质纯棉平纹布料',
      state: '1',
      createTime: new Date(),
      lastChangeTime: null
    },
    {
      material_id: 'MAT002',
      material_name: '涤棉混纺布',
      category_id: 'CAT001002',
      specification: '65%涤35%棉 160g/m²',
      unit: '米',
      current_price: 8.8,
      yearly_prices: [
        {
          year: currentYear,
          price: 8.8,
          effective_date: new Date(),
          supplier: '东方纺织'
        }
      ],
      description: '涤棉混纺面料，耐磨性好',
      state: '1',
      createTime: new Date(),
      lastChangeTime: null
    },
    {
      material_id: 'MAT003',
      material_name: '树脂纽扣',
      category_id: 'CAT002001',
      specification: '直径15mm 4孔',
      unit: '个',
      current_price: 0.08,
      yearly_prices: [
        {
          year: currentYear,
          price: 0.08,
          effective_date: new Date(),
          supplier: '永兴纽扣厂'
        }
      ],
      description: '环保树脂纽扣',
      state: '1',
      createTime: new Date(),
      lastChangeTime: null
    },
    {
      material_id: 'MAT004',
      material_name: '尼龙拉链',
      category_id: 'CAT002002',
      specification: '3号 闭尾拉链',
      unit: '条',
      current_price: 1.2,
      yearly_prices: [
        {
          year: currentYear,
          price: 1.2,
          effective_date: new Date(),
          supplier: 'YKK拉链'
        }
      ],
      description: '高品质尼龙拉链',
      state: '1',
      createTime: new Date(),
      lastChangeTime: null
    },
    {
      material_id: 'MAT005',
      material_name: '涤纶缝纫线',
      category_id: 'CAT002003',
      specification: '40/2 3000码',
      unit: '筒',
      current_price: 3.5,
      yearly_prices: [
        {
          year: currentYear,
          price: 3.5,
          effective_date: new Date(),
          supplier: '金鹰线业'
        }
      ],
      description: '高强度涤纶缝纫线',
      state: '1',
      createTime: new Date(),
      lastChangeTime: null
    }
  ]

  await collection.insertMany(initialMaterials)
  console.log(`✅ 插入 ${initialMaterials.length} 条物料信息初始数据`)
}

// 执行脚本
if (require.main === module) {
  createMaterialTables().catch(console.error)
}
